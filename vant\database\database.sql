-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        5.7.44-log - MySQL Community Server (GPL)
-- 服务器操作系统:                      Win64
-- HeidiSQL 版本:                  12.3.0.6589
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 td_oa_bak 的数据库结构
CREATE DATABASE IF NOT EXISTS `td_oa_bak` /*!40100 DEFAULT CHARACTER SET utf8 */;
USE `td_oa_bak`;

-- 导出  表 td_oa_bak.bpm_back_ext 结构
DROP TABLE IF EXISTS `bpm_back_ext`;
CREATE TABLE IF NOT EXISTS `bpm_back_ext` (
  `run_id` int(10) NOT NULL,
  `other_id` mediumtext,
  `server_id` int(10) NOT NULL,
  UNIQUE KEY `run_id` (`run_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 正在导出表  td_oa_bak.bpm_back_ext 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_controls 结构
DROP TABLE IF EXISTS `bpm_controls`;
CREATE TABLE IF NOT EXISTS `bpm_controls` (
  `control_id` varchar(30) NOT NULL DEFAULT '' COMMENT '控件ID',
  `control_name` varchar(30) NOT NULL DEFAULT '' COMMENT '控件名称',
  `control_no` smallint(5) unsigned NOT NULL COMMENT '控件排序号',
  `control_type` tinyint(3) unsigned NOT NULL COMMENT '控件类型',
  `diag_width` smallint(5) unsigned NOT NULL COMMENT '表单设计器控件属性窗口宽度',
  `diag_height` smallint(5) unsigned NOT NULL COMMENT '表单设计器控件属性窗口高度',
  `sys_flag` tinyint(3) unsigned NOT NULL COMMENT '是否为系统内置(1-是,0-否)',
  PRIMARY KEY (`control_id`),
  KEY `control_no` (`control_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='表单控件定义表';

-- 正在导出表  td_oa_bak.bpm_controls 的数据：~25 rows (大约)
INSERT INTO `bpm_controls` (`control_id`, `control_name`, `control_no`, `control_type`, `diag_width`, `diag_height`, `sys_flag`) VALUES
	('AutoNumber', '编号控件', 180, 0, 417, 350, 1),
	('Calc', '计算控件', 90, 0, 380, 310, 1),
	('Calendar', '日历控件', 80, 0, 380, 230, 1),
	('Checkbox', '复选框', 50, 0, 370, 340, 1),
	('CounterSign', '会签控件', 210, 0, 900, 650, 1),
	('DataSelection', '数据选择控件', 120, 0, 500, 350, 1),
	('ExtDataSelection', '外部数据选择控件', 130, 0, 500, 350, 1),
	('FileUpload', '附件上传控件', 170, 0, 380, 160, 1),
	('FormDataSelection', '表单数据控件', 140, 0, 500, 350, 1),
	('ImageUpload', '图片上传控件', 160, 0, 380, 210, 1),
	('ListView', '列表控件', 60, 0, 950, 425, 1),
	('Location', '定位控件', 230, 0, 500, 350, 1),
	('Macro', '宏控件', 70, 0, 380, 250, 1),
	('MobileSign', '移动签章控件', 190, 0, 380, 210, 1),
	('MobileWriteSign', '移动手写签章控件', 200, 0, 400, 220, 1),
	('Ocr', '票据识别控件', 210, 0, 800, 700, 1),
	('OrgSelect', '部门人员控件', 100, 0, 380, 210, 1),
	('ProgressBar', '进度条控件', 150, 0, 380, 210, 1),
	('QRCode', '二维码控件', 180, 0, 380, 260, 1),
	('Radio', '单选框', 40, 0, 370, 340, 1),
	('Select', '下拉菜单', 30, 0, 480, 310, 1),
	('Signature', '数字签名控件', 105, 0, 380, 210, 1),
	('Text', '单行输入框', 10, 0, 260, 320, 1),
	('Textarea', '多行输入框', 20, 0, 300, 300, 1),
	('WebSign', '签章控件', 110, 0, 380, 260, 1);

-- 导出  表 td_oa_bak.bpm_data_ext 结构
DROP TABLE IF EXISTS `bpm_data_ext`;
CREATE TABLE IF NOT EXISTS `bpm_data_ext` (
  `id` int(10) NOT NULL,
  `flow_list` mediumtext,
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 正在导出表  td_oa_bak.bpm_data_ext 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_entrust_view 结构
DROP TABLE IF EXISTS `bpm_entrust_view`;
CREATE TABLE IF NOT EXISTS `bpm_entrust_view` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `prcs_key_id` int(10) unsigned NOT NULL,
  `run_id` int(10) unsigned NOT NULL DEFAULT '0',
  `flow_prcs` int(10) unsigned NOT NULL DEFAULT '0',
  `prcs_id` int(10) unsigned NOT NULL DEFAULT '0',
  `dept_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '委托部门、传阅部门',
  `create_user` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '委托人、送阅人',
  `user_uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '被委托人、被送阅人',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `read_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '审阅时间',
  `is_view` tinyint(3) NOT NULL DEFAULT '1' COMMENT '1-传阅,0-委托',
  `view_content` varchar(100) DEFAULT '' COMMENT '1-表单，2-公共附件，8-流程关联，3-会签意见区，7-流程图，7-二维码',
  PRIMARY KEY (`id`),
  KEY `run_id` (`run_id`),
  KEY `prcs_key_id` (`prcs_key_id`),
  KEY `user_uid` (`user_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8;

-- 正在导出表  td_oa_bak.bpm_entrust_view 的数据：~2 rows (大约)
INSERT INTO `bpm_entrust_view` (`id`, `prcs_key_id`, `run_id`, `flow_prcs`, `prcs_id`, `dept_id`, `create_user`, `user_uid`, `create_time`, `read_time`, `is_view`, `view_content`) VALUES
	(1, 41, 9, 5, 5, 4, 7, 2, 1719888364, 0, 1, ''),
	(2, 41, 9, 5, 5, 4, 7, 14, 1719888364, 0, 1, '');

-- 导出  表 td_oa_bak.bpm_feedback_common 结构
DROP TABLE IF EXISTS `bpm_feedback_common`;
CREATE TABLE IF NOT EXISTS `bpm_feedback_common` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增唯一ID',
  `USER_ID` varchar(50) NOT NULL COMMENT '用户ID',
  `CONTENT` mediumtext COMMENT '会签意见常用语内容',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `UPDATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM会签意见常用语';

-- 正在导出表  td_oa_bak.bpm_feedback_common 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_flow_hook 结构
DROP TABLE IF EXISTS `bpm_flow_hook`;
CREATE TABLE IF NOT EXISTS `bpm_flow_hook` (
  `hid` int(11) NOT NULL AUTO_INCREMENT COMMENT '引擎ID',
  `flow_id` int(11) NOT NULL COMMENT '流程ID',
  `hname` varchar(40) NOT NULL COMMENT '业务引擎名称',
  `hdesc` varchar(200) NOT NULL COMMENT '描述',
  `hmodule` varchar(40) NOT NULL COMMENT '模块',
  `plugin` varchar(100) NOT NULL COMMENT '调用插件',
  `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态(0-停用,1-必选,可选状态无效)',
  `map` mediumtext NOT NULL COMMENT '数据关系映射',
  `map_in` mediumtext NOT NULL COMMENT '数据关系映射',
  `condition` mediumtext NOT NULL COMMENT '条件列表',
  `condition_set` mediumtext NOT NULL COMMENT '条件公式',
  `system` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否系统内置(0-否,1-是)',
  `corp_id` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `from_module` varchar(40) NOT NULL COMMENT '区分来自工作流还是流程中心',
  PRIMARY KEY (`hid`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='业务引擎';

-- 正在导出表  td_oa_bak.bpm_flow_hook 的数据：~2 rows (大约)
INSERT INTO `bpm_flow_hook` (`hid`, `flow_id`, `hname`, `hdesc`, `hmodule`, `plugin`, `status`, `map`, `map_in`, `condition`, `condition_set`, `system`, `corp_id`, `from_module`) VALUES
	(1, 0, '加班登记', '加班登记引擎', 'attendance_overtime', '', 0, '', '', '', '', 1, '', 'approve_center'),
	(2, 0, '请假登记', '请假登记引擎', 'attend_leave', '', 0, '', '', '', '', 1, '', 'approve_center');

-- 导出  表 td_oa_bak.bpm_form_countersign 结构
DROP TABLE IF EXISTS `bpm_form_countersign`;
CREATE TABLE IF NOT EXISTS `bpm_form_countersign` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一自增ID',
  `run_id` int(10) NOT NULL COMMENT '流水号',
  `flow_prcs` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '设计步骤号',
  `prcs_name` varchar(100) NOT NULL DEFAULT '' COMMENT '步骤名称',
  `sign_content` text NOT NULL COMMENT '会签内容',
  `sign_user` varchar(100) NOT NULL DEFAULT '' COMMENT '会签人',
  `sign_time` varchar(100) NOT NULL DEFAULT '' COMMENT '会签时间',
  `sign_org` varchar(100) NOT NULL DEFAULT '' COMMENT '会签部门',
  `field_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '变量ID',
  `uid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会签人的UID',
  `priv_no` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '角色排序号',
  `user_no` int(10) DEFAULT '0' COMMENT '用户排序号',
  `dept_no` varchar(200) NOT NULL DEFAULT '' COMMENT '部门排序号',
  `sign_data` text NOT NULL COMMENT '签章数据',
  `sign_role` varchar(100) NOT NULL DEFAULT '' COMMENT '会签角色',
  `sole_uid` varchar(40) NOT NULL DEFAULT '' COMMENT '唯一UID',
  `select_check` varchar(100) DEFAULT NULL COMMENT '下拉选中值',
  `radio_check` varchar(100) DEFAULT NULL COMMENT '单选选中值',
  `attachment_id` text NOT NULL,
  `attachment_name` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `run_id` (`run_id`) USING BTREE,
  KEY `uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COMMENT='会签控件';

-- 正在导出表  td_oa_bak.bpm_form_countersign 的数据：~1 rows (大约)
INSERT INTO `bpm_form_countersign` (`id`, `run_id`, `flow_prcs`, `prcs_name`, `sign_content`, `sign_user`, `sign_time`, `sign_org`, `field_id`, `uid`, `priv_no`, `user_no`, `dept_no`, `sign_data`, `sign_role`, `sole_uid`, `select_check`, `radio_check`, `attachment_id`, `attachment_name`) VALUES
	(1, 4, 2, '', '同意', '陈强', '2024-07-02', '', 743, 15, 2, 10, '001', '', '', '', NULL, NULL, '', '');

-- 导出  表 td_oa_bak.bpm_form_field 结构
DROP TABLE IF EXISTS `bpm_form_field`;
CREATE TABLE IF NOT EXISTS `bpm_form_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `form_id` int(10) NOT NULL COMMENT '表单ID',
  `field_id` int(10) NOT NULL COMMENT '字段ID',
  `filed_name` varchar(64) NOT NULL COMMENT '字段名称',
  `filed_type` varchar(64) NOT NULL COMMENT '字段类型',
  `field_data` mediumtext COMMENT '字段数据',
  `form_version_id` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `form_id` (`form_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8 COMMENT='表单字段信息';

-- 正在导出表  td_oa_bak.bpm_form_field 的数据：~1 rows (大约)
INSERT INTO `bpm_form_field` (`id`, `form_id`, `field_id`, `filed_name`, `filed_type`, `field_data`, `form_version_id`) VALUES
	(12, 28, 17, '部门负责人', 'countersign', 'a:14:{s:11:"totalheight";s:0:"";s:8:"signleft";s:0:"";s:5:"signy";s:0:"";s:10:"signheight";s:0:"";s:8:"timetype";s:4:"date";s:6:"custom";s:0:"";s:8:"fontsize";s:0:"";s:14:"countsignorder";s:1:"0";s:9:"handwrite";s:1:"0";s:7:"addseal";s:1:"0";s:7:"opinion";s:1:"0";s:6:"parase";s:1:"0";s:8:"textarea";s:0:"";s:13:"editorcontent";s:299:"<p><span class="{macro}" contenteditable="false" title="会签区域">{signcontent}</span></p><p style="text-align: right;"><span class="{macro}" contenteditable="false" title="会签人">{signuser}</span>&nbsp;<span class="{macro}" contenteditable="false" title="会签时间">{signtime}</span></p>";}', 0);

-- 导出  表 td_oa_bak.bpm_form_sort 结构
DROP TABLE IF EXISTS `bpm_form_sort`;
CREATE TABLE IF NOT EXISTS `bpm_form_sort` (
  `SORT_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `SORT_NO` int(11) NOT NULL DEFAULT '0' COMMENT '分类排序号',
  `SORT_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '分类名称',
  `SORT_PARENT` int(11) NOT NULL DEFAULT '0' COMMENT '上级分类ID',
  `HAVE_CHILD` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否存在下级分类(0-否,1-是)',
  `DEPT_ID` int(11) NOT NULL DEFAULT '0' COMMENT '所属部门ID',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`SORT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='表单分类';

-- 正在导出表  td_oa_bak.bpm_form_sort 的数据：~1 rows (大约)
INSERT INTO `bpm_form_sort` (`SORT_ID`, `SORT_NO`, `SORT_NAME`, `SORT_PARENT`, `HAVE_CHILD`, `DEPT_ID`, `CORP_ID`) VALUES
	(2, 1, '行政管理', 0, 0, 0, '');

-- 导出  表 td_oa_bak.bpm_form_type 结构
DROP TABLE IF EXISTS `bpm_form_type`;
CREATE TABLE IF NOT EXISTS `bpm_form_type` (
  `FORM_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '表单ID',
  `FORM_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '表单名称',
  `PRINT_MODEL` longtext NOT NULL COMMENT '表单设计信息',
  `PRINT_MODEL_SHORT` longtext NOT NULL COMMENT '精简后的表单设计信息',
  `DEPT_ID` int(11) NOT NULL DEFAULT '0' COMMENT '表单所属部门',
  `SCRIPT` mediumtext NOT NULL COMMENT '表单拓展脚本',
  `CSS` mediumtext NOT NULL COMMENT '表单扩展样式',
  `ITEM_MAX` int(11) NOT NULL COMMENT '最大的项目编号',
  `FORM_SORT` int(11) NOT NULL DEFAULT '0' COMMENT '表单所属分类',
  `IS_NEW` int(11) NOT NULL DEFAULT '1' COMMENT '表单类型 1 - 新表单 0 - 老表单',
  `FLOW_ID` int(11) DEFAULT NULL COMMENT '流程ID',
  `FLOW_PRCS` int(11) DEFAULT NULL COMMENT '设计步骤',
  `MOBILE_PRINT_MODEL` mediumtext COMMENT '移动表单设计信息',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`FORM_ID`),
  KEY `DEPT_ID` (`DEPT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8 COMMENT='BPM表单';

-- 正在导出表  td_oa_bak.bpm_form_type 的数据：~1 rows (大约)
INSERT INTO `bpm_form_type` (`FORM_ID`, `FORM_NAME`, `PRINT_MODEL`, `PRINT_MODEL_SHORT`, `DEPT_ID`, `SCRIPT`, `CSS`, `ITEM_MAX`, `FORM_SORT`, `IS_NEW`, `FLOW_ID`, `FLOW_PRCS`, `MOBILE_PRINT_MODEL`, `CORP_ID`) VALUES
	(6, '员工请假申请表', '<link href="/static/modules/workflow/system/flow_form/css/form_adv.css" rel="stylesheet" type="text/css"/><div class="form-wrap" style="text-align: center;"><table width="630" tabindex="65535" class="xdLayout" style="table-layout: fixed; width: 631px;"><colgroup class="ue-tableresize-colgroup"><col style="width: 85px;"/><col style="width: 125px;"/><col style="width: 75px;"/><col style="width: 110px;"/><col style="width: 75px;"/><col style="width: 160px;"/></colgroup><tbody valign="top"><tr class="firstRow"><td align="center" colspan="6" style="word-break: break-all;"><p class="form-title"><strong>请假申请表</strong></p></td></tr><tr><td colspan="6"><div class="form-group-title"><strong>申请信息</strong></div></td></tr><tr class="form-item"><td style="word-break: break-all;"><div><span class="form-item-field"><em class="form-mid-placeholder"></em>申请部门：</span></div></td><td style="word-break: break-all;"><input title="申请人所在部门" class="AUTO" datafld="SYS_DEPTNAME_SHORT" hidden="0" style="" uid="E46845026C3B8C6127FA9B04F64904D6" name="DATA_218" value="{MACRO}" type="text"/></td><td style="word-break: break-all;"><div align="center"><span class="form-item-field">申请人：</span></div></td><td style="word-break: break-all;"><input title="申请人" class="AUTO" datafld="SYS_USERNAME" hidden="0" style="width: 100px;" uid="E5D0E4C6F07C687F932EDCBD51BAF2B8" value="{MACRO}" type="text" name="DATA_219"/></td><td style="word-break: break-all;"><div align="center"><span class="form-item-field">登记时间：</span></div></td><td style="word-break: break-all;"><input title="登记时间" class="AUTO" datafld="SYS_DATETIME" hidden="0" style="width: 140px;" uid="50D721413EE485FEB9AF257765358357" value="{MACRO}" type="text" name="DATA_220"/></td></tr><tr><td style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>流<em class="form-min-placeholder"></em>程<em class="form-min-placeholder"></em>号：</span></td><td colspan="5" valign="middle">#[MACRO_RUN_NAME]</td></tr><tr class="form-item"><td><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>请假类别：</span></div></td><td colspan="5" style="word-break: break-all;"><img title="请假类别" radio_check="同意" radio_field="事假&#96;年假&#96;探亲假&#96;病假&#96;婚假&#96;产假&#96;丧假&#96;" src="/static/images/form/radio.png" class="RADIO" classname="RADIO" uid="57C429D16EF06A31F2E1BC2E0B463E89" name="DATA_221"/></td></tr><tr class="form-item"><td><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>请假时间：</span></div></td><td valign="middle" colspan="5"><div><img title="日期控件:开始时间" date_format="yyyy-MM-dd HH:mm:ss" border="0" align="absMiddle" class="DATE" classname="DATE" src="/static/images/form/calendar.png" style="cursor: pointer;width:18px;height:18px;" alt="" uid="E44168C1781D4F47271A698CDE0D5DD8" name="OTHER_5"/><em class="form-min-placeholder"></em>到<em class="form-min-placeholder"></em><img title="日期控件:结束时间" date_format="yyyy-MM-dd HH:mm:ss" border="0" align="absMiddle" class="DATE" classname="DATE" src="/static/images/form/calendar.png" style="cursor: pointer;width:18px;height:18px;" alt="" uid="B3759F8A918780BE7A6B230D9BE33838" name="OTHER_6"/><em class="form-mid-placeholder"></em> <em class="form-min-placeholder"></em>共<em class="form-min-placeholder"></em><input type="text" align="left" hidden="0" title="天数" style="text-align:left;width:30px;" validation="type:text;" uid="E792FBB2078FBC99831CEDF704616135" value="" name="DATA_222"/><em class="form-min-placeholder"></em>天</div></td></tr><tr class="form-item" style="border-bottom:none"><td rowspan="2"><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>事<em class="form-large-placeholder"></em>由：</span></div></td><td colspan="5"><textarea title="请假事由" rich="0" style="width: 520px;height: 60px;" uid="8F17C2A356E95CDA4A8A45634A551041" name="DATA_223"></textarea></td></tr><tr class="form-item"><td colspan="5"><div><span class="form-item-note">* 注：天数和申请理由为必填项</span></div></td></tr><tr><td colspan="6"><div class="form-group-title"><strong>审核信息</strong></div></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>上级主管审批：<img title="上级主管审批" radio_check="同意" radio_field="同意&#96;不同意&#96;" src="/static/images/form/radio.png" class="RADIO" classname="RADIO" uid="64C611BF362BA3958E82ADE89C81EA8E" name="DATA_224"/></span></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><em class="form-mid-placeholder"></em><textarea title="上级主管审批意见" rich="0" style="width: 593px;height: 60px;" uid="4260770090EE10403930194603F2C70F" name="DATA_225"></textarea><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right"><input title="上级主管审批签字" class="AUTO" datafld="SYS_USERNAME_DATE" hidden="0" style="" uid="42A7F826CB7A467D5D8E4311CC0B0451" value="{MACRO}" type="text" name="DATA_226"/><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>部门总监审批：<img title="部门总监审批" radio_check="同意" radio_field="同意&#96;不同意&#96;" src="/static/images/form/radio.png" class="RADIO" classname="RADIO" uid="4C6957582CA14B608D1FE77612DD730F" name="DATA_227"/></span></td></tr><tr class="form-item"><td colspan="6"><em class="form-mid-placeholder"></em><textarea title="部门总监意见" rich="0" style="width: 593px;height: 60px;" uid="E0112AE010804D0E87E476680FA63E80" name="DATA_228"></textarea><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right"><input title="部门总监签字" class="AUTO" datafld="SYS_USERNAME_DATE" hidden="0" style="" uid="8B569405F7CA4769BAACD96E71AEBEBD" value="{MACRO}" type="text" name="DATA_229"/><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6"><span class="form-item-field"><em class="form-mid-placeholder"></em>CEO审批：<img title="CEO审批" radio_check="同意" radio_field="同意&#96;不同意&#96;" src="/static/images/form/radio.png" class="RADIO" classname="RADIO" uid="D88166279B6845C68CA3A1DC068215B0" name="DATA_230"/></span></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><em class="form-mid-placeholder"></em><textarea title="CEO意见" rich="0" style="width: 593px;height: 60px;" uid="DC170729801ADFA44114B93652112EAF" name="DATA_231"></textarea><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right"><input title="CEO签字" class="AUTO" datafld="SYS_USERNAME_DATE" hidden="0" style="" uid="524DBAEDC1BF1DD248D3FC9CD1FCB021" value="{MACRO}" type="text" name="DATA_232"/><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6"><span class="form-item-field"><em class="form-mid-placeholder"></em>人力资源经理审批：<img title="人力资源经理审批" radio_check="同意" radio_field="同意&#96;不同意&#96;" src="/static/images/form/radio.png" class="RADIO" classname="RADIO" uid="8F381C663282C8831A819E9F9BA649E7" name="DATA_233"/></span></td></tr><tr class="form-item"><td colspan="6"><em class="form-mid-placeholder"></em><textarea title="人力资源经理意见" rich="0" style="width: 593px;height: 60px;" uid="D4EBAF3D8E3F761B348A6F3C699D5206" name="DATA_234"></textarea><em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right"><input title="人力资源经理签字" class="AUTO" datafld="SYS_USERNAME_DATE" hidden="0" style="" uid="9C47CF014403BD52C99303AD3A774138" value="{MACRO}" type="text" name="DATA_235"/><em class="form-mid-placeholder"></em></td></tr></tbody></table><input type="text" align="left" name="DATA_236" hidden="1" title="主键ID" style="text-align:left;" validation="type:text;len:;" uid="D358731F9F4A8DD20887013749E6BB58"/></div>', '<link href="/static/modules/workflow/system/flow_form/css/form_adv.css" rel="stylesheet" type="text/css"/><div class="form-wrap" style="text-align: center;"><table width="630" tabindex="65535" class="xdLayout" style="table-layout: fixed; width: 631px;"><colgroup class="ue-tableresize-colgroup"><col style="width: 85px;"/><col style="width: 125px;"/><col style="width: 75px;"/><col style="width: 110px;"/><col style="width: 75px;"/><col style="width: 160px;"/></colgroup><tbody valign="top"><tr class="firstRow"><td align="center" colspan="6" style="word-break: break-all;"><p class="form-title"><strong>请假申请表</strong></p></td></tr><tr><td colspan="6"><div class="form-group-title"><strong>申请信息</strong></div></td></tr><tr class="form-item"><td style="word-break: break-all;"><div><span class="form-item-field"><em class="form-mid-placeholder"></em>申请部门：</span></div></td><td style="word-break: break-all;">{DATA_218}</td><td style="word-break: break-all;"><div align="center"><span class="form-item-field">申请人：</span></div></td><td style="word-break: break-all;">{DATA_219}</td><td style="word-break: break-all;"><div align="center"><span class="form-item-field">登记时间：</span></div></td><td style="word-break: break-all;">{DATA_220}</td></tr><tr><td style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>流<em class="form-min-placeholder"></em>程<em class="form-min-placeholder"></em>号：</span></td><td colspan="5" valign="middle">#[MACRO_RUN_NAME]</td></tr><tr class="form-item"><td><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>请假类别：</span></div></td><td colspan="5" style="word-break: break-all;">{DATA_221}</td></tr><tr class="form-item"><td><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>请假时间：</span></div></td><td valign="middle" colspan="5"><div>{OTHER_5}<em class="form-min-placeholder"></em>到<em class="form-min-placeholder"></em>{OTHER_6}<em class="form-mid-placeholder"></em> <em class="form-min-placeholder"></em>共<em class="form-min-placeholder"></em>{DATA_222}<em class="form-min-placeholder"></em>天</div></td></tr><tr class="form-item" style="border-bottom:none"><td rowspan="2"><div align="left"><span class="form-item-field"><em class="form-mid-placeholder"></em>事<em class="form-large-placeholder"></em>由：</span></div></td><td colspan="5">{DATA_223}</td></tr><tr class="form-item"><td colspan="5"><div><span class="form-item-note">* 注：天数和申请理由为必填项</span></div></td></tr><tr><td colspan="6"><div class="form-group-title"><strong>审核信息</strong></div></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>上级主管审批：{DATA_224}</span></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><em class="form-mid-placeholder"></em>{DATA_225}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right">{DATA_226}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><span class="form-item-field"><em class="form-mid-placeholder"></em>部门总监审批：{DATA_227}</span></td></tr><tr class="form-item"><td colspan="6"><em class="form-mid-placeholder"></em>{DATA_228}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right">{DATA_229}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6"><span class="form-item-field"><em class="form-mid-placeholder"></em>CEO审批：{DATA_230}</span></td></tr><tr class="form-item"><td colspan="6" style="word-break: break-all;"><em class="form-mid-placeholder"></em>{DATA_231}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right">{DATA_232}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6"><span class="form-item-field"><em class="form-mid-placeholder"></em>人力资源经理审批：{DATA_233}</span></td></tr><tr class="form-item"><td colspan="6"><em class="form-mid-placeholder"></em>{DATA_234}<em class="form-mid-placeholder"></em></td></tr><tr class="form-item"><td colspan="6" align="right">{DATA_235}<em class="form-mid-placeholder"></em></td></tr></tbody></table>{DATA_236}</div>', 0, '', '', 236, 5, 1, 6, 1, NULL, '');

-- 导出  表 td_oa_bak.bpm_form_version 结构
DROP TABLE IF EXISTS `bpm_form_version`;
CREATE TABLE IF NOT EXISTS `bpm_form_version` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FORM_ID` int(11) NOT NULL COMMENT '表单ID',
  `FORM_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '表单名称',
  `PRINT_MODEL` longtext NOT NULL COMMENT '表单设计信息',
  `PRINT_MODEL_SHORT` longtext NOT NULL COMMENT '精简后的表单设计信息',
  `SCRIPT` mediumtext NOT NULL COMMENT '表单拓展脚本',
  `CSS` mediumtext NOT NULL COMMENT '表单扩展样式',
  `TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `MARK` int(11) NOT NULL COMMENT '版本号',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `ITEM_MAX` int(11) NOT NULL COMMENT '最大的项目编号',
  `USE_FLAG` tinyint(3) NOT NULL DEFAULT '0' COMMENT '表单是否可用',
  `MOBILE_PRINT_MODEL` mediumtext COMMENT '移动表单设计信息',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='BPM表单版本库';

-- 正在导出表  td_oa_bak.bpm_form_version 的数据：~1 rows (大约)
INSERT INTO `bpm_form_version` (`ID`, `FORM_ID`, `FORM_NAME`, `PRINT_MODEL`, `PRINT_MODEL_SHORT`, `SCRIPT`, `CSS`, `TIME`, `MARK`, `CORP_ID`, `ITEM_MAX`, `USE_FLAG`, `MOBILE_PRINT_MODEL`) VALUES
	(1, 23, '合同评审审批表', '<p style="text-align: center; margin-top: 10px; margin-bottom: 15px;"><strong><span style="font-family: 仿宋, FangSong; font-size: 24px;">合同评审审批表</span></strong></p><table width="692" align="center" style="width: 692px; table-layout: fixed;" cellspacing="0" data-sort="sortDisabled"><colgroup class="ue-tableresize-colgroup"><col style="width: 105px;"/><col style="width: 105px;"/><col style="width: 100px;"/><col style="width: 108px;"/><col style="width: 29px;"/><col style="width: 72px;"/><col style="width: 93px;"/><col style="width: 96px;"/></colgroup><tbody><tr class="firstRow" style="height: 29px;"><td valign="center" style="padding: 0px; border: 1px solid rgb(0, 0, 0); text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同名称</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; -ms-word-break: break-all;" colspan="7"><p>&nbsp;<input name="DATA_83" title="合同名称" align="left" style="text-align:left;width:593px;" type="text" value="应当填写合同全称" hidden="0" uid="3C83770B5DD01BA528063B5751E7D136" validation="type:text;"/></p></td></tr><tr style="height: 29px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同编号</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="3"><p style="text-align: left;"><span style="color: rgb(0, 0, 0); font-family: arial, helvetica, sans-serif; font-size: 14px;"><input name="DATA_84" title="编号_年" class="AUTO" datafld="SYS_DATE_CN_SHORT4" style="width: 35px; font-size: 14px;" type="text" value="{MACRO}" hidden="0" uid="50586B9926A34D33EB8D4FFCAAAA6F29"/><span style="color: rgb(0, 0, 0); font-size: 14px;">年-<select name="DATA_85" title="合同类型简称" style="width: 50px;" uid="9D527FEAFC0F87FF6F2EE406F22D8E81" contenteditable="false"><option value="" selected="selected"></option><option value="TZ|投资合同">TZ|投资合同</option><option value="YY|运营合同">YY|运营合同</option><option value="CG|采购合同">CG|采购合同</option><option value="ZJ|资金合同">ZJ|资金合同</option><option value="HZ|对外合作合同">HZ|对外合作合同</option><option value="ZX|服务咨询合同">ZX|服务咨询合同</option><option value="QT|其他合同">QT|其他合同</option><option value="ZL|战略合作协议">ZL|战略合作协议</option></select>-<select name="DATA_86" title="部门简称" style="width: 50px;" uid="307E6E95E2DFCE8A25BFF113984896E6" contenteditable="false"><option value="" selected="selected"></option><option value="BG|办公室">BG|办公室</option><option value="DQ|党群工作部">DQ|党群工作部</option><option value="SF|审计法务部">SF|审计法务部</option><option value="RL|人力资源部">RL|人力资源部</option><option value="CW|财务管理部">CW|财务管理部</option><option value="YY|运营管理部">YY|运营管理部</option><option value="TZ|投资经营部">TZ|投资经营部</option></select>-<input name="DATA_128" title="编号" class="AUTO" datafld="SYS_AUTONUM_YEAR" style="width: 35px; font-size: 14px;" type="text" value="{MACRO}" hidden="0" uid="7A1CE15A70F3786B7967C56748CBB3CF"/>号</span></span></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同签订日期</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center; -ms-word-break: break-all;" colspan="2">&nbsp;<img name="DATA_91" title="日期控件:合同签订日期" align="absMiddle" class="DATE" style="width: 18px; height: 18px; cursor: pointer;" alt="" src="/static/images/form/calendar.png" border="0" uid="DB99CB838BF3908A8346CC418E40BA0D" datewidth="70" classname="DATE" date_format="yyyy-MM-dd"/></td></tr><tr><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; height: 29px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">送审单位</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center; -ms-word-break: break-all;"><select name="DATA_100" title="送审部门" style="width: 100px;" uid="4CD7CD4D13D9945BA7D549D139C3A182" child="部门简称" contenteditable="false"><option value="办公室">办公室</option><option value="党群工作部">党群工作部</option><option value="投资经营部">投资经营部</option><option value="运营管理部">运营管理部</option><option value="财务管理部">财务管理部</option><option value="人力资源部">人力资源部</option><option value="审计法务部">审计法务部</option><option value="" selected="selected"></option></select></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">形成方式</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;"><select name="DATA_88" title="形成方式" style="width: 100px;" uid="50E68854F0888D9611510E6052C1EEEB" contenteditable="false"><option value="投标">投标</option><option value="议标">议标</option><option value="询价">询价</option><option value="战略合作">战略合作</option><option value="委托形成">委托形成</option><option value="" selected="selected"></option></select></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同类型</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" colspan="2"><select name="DATA_90" title="合同类型" style="width: 100px;" uid="E9651E54D41F3C1C9AB760F7D3BBEB64" child="合同类型简称" contenteditable="false"><option value="投资合同">投资合同</option><option value="运营合同">运营合同</option><option value="采购合同">采购合同</option><option value="资金合同">资金合同</option><option value="服务咨询合同">服务咨询合同</option><option value="战略合作协议">战略合作协议</option><option value="其他合同">其他合同</option><option value="" selected="selected"></option></select></td></tr><tr style="height: 27px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: center; -ms-word-break: break-all;" rowspan="4"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同简述</span></strong></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" rowspan="2" colspan="7"><p style="text-align: left;"><img name="DATA_93" title="合同相对方" align="absMiddle" class="LIST_VIEW" alt="" src="/static/images/form/listview.png" border="0" default_cols="2" lv_title="合同方&#96;公司名称&#96;经办部门&#96;法定代表人&#96;联系人&#96;联系电话&#96;" lv_sum="0&#96;0&#96;0&#96;0&#96;0&#96;0&#96;" lv_size="70&#96;192&#96;70&#96;70&#96;70&#96;104&#96;" lv_colvalue="&#96;&#96;&#96;&#96;&#96;&#96;" lv_coltype="text&#96;text&#96;text&#96;text&#96;text&#96;text&#96;" lv_cal="&#96;&#96;&#96;&#96;&#96;&#96;" datatype="1" lv_value="1" variable_sort_uid="31C44EC959BD2232B99CD0850F8379E4" variable_id="31B9B1765CC05F2A7FFF9BBC7D69115F&#96;C6D211BED7B19301ED417B0DEF5CEB4A&#96;A1BDB588E736370FF9B95EC6AE297A49&#96;0D591E8DD93E2E93FF2C8D3B34B4F6F7&#96;D827963D2C090381BD8C27C476B9414E&#96;9C07B3317DE662AACBD6669DAD460C08&#96;" new_width="true" variable_sort_id="65"/></p></td></tr><tr style="height: 27px;"></tr><tr style="height: 27px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">履约期限（天）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;"><input name="DATA_101" title="履约期限" align="left" style="width: 60px; text-align: left;" type="text" value="" hidden="0" uid="C468A57BFB21D32AAAD28AC6A5947155" validation="type:number;point:;"/></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">合同金额（</span></strong><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">元</span><span style="font-family: 仿宋, FangSong; font-size: 14px;">）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;" colspan="2"><input name="DATA_102" title="合同金额" align="left" style="width: 60px; text-align: left;" type="text" value="" hidden="0" uid="008CFB66CD46750B895A645E65372E07" validation="type:number;point:2;"/></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">增值税（元）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;"><input name="DATA_103" title="增值税" align="left" style="width: 60px; text-align: left;" type="text" value="" hidden="0" uid="0B41392FF09508A808FCEE5AAB6163D5" validation="type:number;point:2;"/></td></tr><tr style="height: 65px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" rowspan="1" colspan="1"><p style="text-align: center;"><span style="font-size: 14px;"><strong><span style="font-family: 仿宋, FangSong;">主要条款</span></strong></span></p></td><td align="null" valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" rowspan="1" colspan="6"><textarea name="DATA_104" title="主要条款" style="width: 494px;height: 63px;" uid="1BACB43DCC8A42CE33590B874F1F7C56" rich="0"></textarea></td></tr><tr style="height: 100px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;" rowspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">送审部门意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><textarea title="送审部门意见" fontsize="" rich="0" style="width: 599px; height: 98px;" uid="4B12D773A459FE77E18EACA9C382DE4B" name="DATA_154"></textarea></p></td></tr><tr style="height: 37px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong;">经办人</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" rowspan="1" colspan="2"><input name="DATA_107" title="经办人" class="AUTO" datafld="SYS_USERNAME" style="width: 100px;" type="text" value="{MACRO}" hidden="0" uid="B0DC0EB264C9974CAF56BEB17AFE64A6"/></td><td align="null" valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" rowspan="1" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong;">日期</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" colspan="2"><input name="DATA_108" title="日期" class="AUTO" datafld="SYS_DATE" style="width: 100px;" type="text" value="{MACRO}" hidden="0" uid="6FCFC587CACFD99DAC66906C5BDA882D"/></td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">评审部门意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><img class="COUNTERSIGN" uid="B6C58C60A3F88DE82C547A5A75FC13FC" name="DATA_155" title="评审部门意见" src="/static/images/form/countersign.png"/></p></td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">评审意见</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">落实情况</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><img class="COUNTERSIGN" uid="7BC1063B195A4B5DDA154708830229F8" name="DATA_156" title="评审意见落实情况" src="/static/images/form/countersign.png"/></p></td></tr><tr style="height: 93px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">分管领导</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">意&nbsp;见</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><img class="COUNTERSIGN" uid="0952B44545E5562C0FB271BCC36269C1" name="DATA_157" title="分管领导意见" src="/static/images/form/countersign.png"/></p></td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">法律事务</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">分管领导意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="text-align: left; color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><img class="COUNTERSIGN" uid="B1805B4F437309D33D6C45E289AC27E8" name="DATA_158" title="法律事务分管领导意见" src="/static/images/form/countersign.png"/></p></td></tr><tr style="height: 92px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">主管领导</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">意&nbsp;&nbsp;见</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);"><img class="COUNTERSIGN" uid="160C68ACE62B62F256AEE2941B1BEDB3" name="DATA_159" title="主管领导意见" src="/static/images/form/countersign.png"/></p></td></tr></tbody></table><p style="text-align: center;"><span style="font-size: 14px;"></span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><br/></p>', '<p style="text-align: center; margin-top: 10px; margin-bottom: 15px;"><strong><span style="font-family: 仿宋, FangSong; font-size: 24px;">合同评审审批表</span></strong></p><table width="692" align="center" style="width: 692px; table-layout: fixed;" cellspacing="0" data-sort="sortDisabled"><colgroup class="ue-tableresize-colgroup"><col style="width: 105px;"/><col style="width: 105px;"/><col style="width: 100px;"/><col style="width: 108px;"/><col style="width: 29px;"/><col style="width: 72px;"/><col style="width: 93px;"/><col style="width: 96px;"/></colgroup><tbody><tr class="firstRow" style="height: 29px;"><td valign="center" style="padding: 0px; border: 1px solid rgb(0, 0, 0); text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同名称</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; -ms-word-break: break-all;" colspan="7"><p>&nbsp;{DATA_83}</p></td></tr><tr style="height: 29px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同编号</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="3"><p style="text-align: left;"><span style="color: rgb(0, 0, 0); font-family: arial, helvetica, sans-serif; font-size: 14px;">{DATA_84}<span style="color: rgb(0, 0, 0); font-size: 14px;">年-{DATA_85}-{DATA_86}-{DATA_128}号</span></span></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同签订日期</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center; -ms-word-break: break-all;" colspan="2">&nbsp;{DATA_91}</td></tr><tr><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; height: 29px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">送审单位</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center; -ms-word-break: break-all;">{DATA_100}</td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">形成方式</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;">{DATA_88}</td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同类型</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" colspan="2">{DATA_90}</td></tr><tr style="height: 27px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: center; -ms-word-break: break-all;" rowspan="4"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">合同简述</span></strong></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" rowspan="2" colspan="7"><p style="text-align: left;">{DATA_93}</p></td></tr><tr style="height: 27px;"></tr><tr style="height: 27px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">履约期限（天）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;">{DATA_101}</td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">合同金额（</span></strong><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">元</span><span style="font-family: 仿宋, FangSong; font-size: 14px;">）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;" colspan="2">{DATA_102}</td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 14px;">增值税（元）</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: right;">{DATA_103}</td></tr><tr style="height: 65px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" rowspan="1" colspan="1"><p style="text-align: center;"><span style="font-size: 14px;"><strong><span style="font-family: 仿宋, FangSong;">主要条款</span></strong></span></p></td><td align="null" valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left; -ms-word-break: break-all;" rowspan="1" colspan="6">{DATA_104}</td></tr><tr style="height: 100px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;" rowspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">送审部门意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_154}</p></td></tr><tr style="height: 37px;"><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong;">经办人</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" rowspan="1" colspan="2">{DATA_107}</td><td align="null" valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: left;" rowspan="1" colspan="2"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong;">日期</span></strong></p></td><td valign="center" style="border-width: 1px 1px 1px medium; border-style: solid solid solid none; border-color: rgb(0, 0, 0) rgb(0, 0, 0) rgb(0, 0, 0) currentColor; padding: 0px; text-align: center;" colspan="2">{DATA_108}</td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">评审部门意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_155}</p></td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">评审意见</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">落实情况</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_156}</p></td></tr><tr style="height: 93px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">分管领导</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">意&nbsp;见</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_157}</p></td></tr><tr style="height: 84px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px; text-align: left; -ms-word-break: break-all;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">法律事务</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">分管领导意见</span></strong></p></td><td align="left" valign="middle" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; text-align: left; word-break: break-all;" colspan="7"><p style="text-align: left; color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_158}</p></td></tr><tr style="height: 92px;"><td valign="center" style="border-width: medium 1px 1px; border-style: none solid solid; border-color: currentColor rgb(0, 0, 0) rgb(0, 0, 0); padding: 0px;"><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">主管领导</span></strong></p><p style="text-align: center;"><strong><span style="font-family: 仿宋, FangSong; font-size: 16px;">意&nbsp;&nbsp;见</span></strong></p></td><td valign="center" style="border-width: medium 1px 1px medium; border-style: none solid solid none; border-color: currentcolor rgb(0, 0, 0) rgb(0, 0, 0) currentcolor; padding: 0px; word-break: break-all;" colspan="7"><p style="color: rgb(68, 68, 68); font-family: &quot;Microsoft YaHei&quot;, Simsun, Arial, sans-serif; font-size: 14px; margin-top: 0px; margin-bottom: 0px; white-space: normal; background-color: rgb(255, 255, 255);">{DATA_159}</p></td></tr></tbody></table><p style="text-align: center;"><span style="font-size: 14px;"></span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><br/></p>', '', '', '2024-07-02 09:18:25', 1, '', 159, 1, NULL);

-- 导出  表 td_oa_bak.bpm_home_set 结构
DROP TABLE IF EXISTS `bpm_home_set`;
CREATE TABLE IF NOT EXISTS `bpm_home_set` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `flow_id` int(10) NOT NULL DEFAULT '0',
  `user_uid` int(10) NOT NULL DEFAULT '0',
  `flow_order` int(10) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `flow_id` (`flow_id`),
  KEY `user_uid` (`user_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程中心首页设置';

-- 正在导出表  td_oa_bak.bpm_home_set 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_hook 结构
DROP TABLE IF EXISTS `bpm_hook`;
CREATE TABLE IF NOT EXISTS `bpm_hook` (
  `HID` int(11) NOT NULL AUTO_INCREMENT COMMENT '引擎ID',
  `FLOW_ID` int(11) NOT NULL COMMENT '流程ID',
  `HNAME` varchar(40) NOT NULL COMMENT '业务引擎名称',
  `HDESC` varchar(200) NOT NULL COMMENT '描述',
  `hMODULE` varchar(40) NOT NULL COMMENT '模块',
  `PLUGIN` varchar(100) NOT NULL COMMENT '调用插件',
  `STATUS` int(1) NOT NULL DEFAULT '0' COMMENT '状态(0-停用,1-必选,可选状态无效)',
  `MAP` mediumtext NOT NULL COMMENT '数据关系映射',
  `CONDITION` mediumtext NOT NULL COMMENT '条件列表',
  `CONDITION_SET` mediumtext NOT NULL COMMENT '条件公式',
  `SYSTEM` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否系统内置(0-否,1-是)',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`HID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM业务引擎';

-- 正在导出表  td_oa_bak.bpm_hook 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_manage_log 结构
DROP TABLE IF EXISTS `bpm_manage_log`;
CREATE TABLE IF NOT EXISTS `bpm_manage_log` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_ID` int(11) NOT NULL COMMENT '流程ID',
  `FLOW_NAME` varchar(200) NOT NULL COMMENT '流程名称',
  `UID` int(11) NOT NULL COMMENT '操作用户唯一标识即用户表主键ID',
  `TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `IP` varchar(20) NOT NULL COMMENT '操作用户的IP地址',
  `TYPE` int(11) NOT NULL COMMENT '日志类型(1-增加,2-修改,3-删除,但实际1、2类型存的比较混乱)',
  `CONTENT` mediumtext NOT NULL COMMENT '日志内容',
  `USER_ID` varchar(40) NOT NULL COMMENT '用户ID',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`ID`),
  KEY `I_TYPE` (`TYPE`),
  KEY `I_USER_ID` (`USER_ID`),
  KEY `I_TIME` (`TIME`)
) ENGINE=InnoDB AUTO_INCREMENT=264 DEFAULT CHARSET=utf8 COMMENT='BPM管理日志';

-- 正在导出表  td_oa_bak.bpm_manage_log 的数据：~1 rows (大约)
INSERT INTO `bpm_manage_log` (`ID`, `FLOW_ID`, `FLOW_NAME`, `UID`, `TIME`, `IP`, `TYPE`, `CONTENT`, `USER_ID`, `CORP_ID`) VALUES
	(1, 15, '会议纪要', 1, '2024-06-27 18:58:36', '*************', 3, '删除流程：会议纪要', 'admin', '');

-- 导出  表 td_oa_bak.bpm_print_tpl 结构
DROP TABLE IF EXISTS `bpm_print_tpl`;
CREATE TABLE IF NOT EXISTS `bpm_print_tpl` (
  `T_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '模版ID',
  `FLOW_ID` int(11) NOT NULL COMMENT '流程ID',
  `T_TYPE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '模版类型(1-打印模版,2-手写呈批单)',
  `T_NAME` varchar(100) NOT NULL COMMENT '打印模版名称',
  `CONTENT` longtext NOT NULL COMMENT '打印模版内容',
  `FLOW_PRCS` mediumtext NOT NULL COMMENT '可使用该模版的步骤',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`T_ID`),
  KEY `FLOW_ID` (`FLOW_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='打印模版';

-- 正在导出表  td_oa_bak.bpm_print_tpl 的数据：~2 rows (大约)
INSERT INTO `bpm_print_tpl` (`T_ID`, `FLOW_ID`, `T_TYPE`, `T_NAME`, `CONTENT`, `FLOW_PRCS`, `CREATE_TIME`, `CORP_ID`) VALUES
	(1, 26, 2, '发文单', '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***************************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', '', '2024-07-19 09:07:45', ''),
	(2, 58, 2, '文件传阅单', '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', '', '2024-07-19 09:07:16', '');

-- 导出  表 td_oa_bak.bpm_priv 结构
DROP TABLE IF EXISTS `bpm_priv`;
CREATE TABLE IF NOT EXISTS `bpm_priv` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_ID` int(11) NOT NULL COMMENT '流程ID',
  `PRIV_TYPE` int(11) NOT NULL COMMENT '授权类型(1-管理,2-监控,3-查询,4-编辑,5-点评)',
  `PRIV_SCOPE` mediumtext NOT NULL COMMENT '管理范围[SELF_ORG-本机构,ALL_DEPT-所有部门,SELF_DEPT-本部门,部门ID串]',
  `USER` mediumtext NOT NULL COMMENT '按人员设定授权范围',
  `DEPT` mediumtext NOT NULL COMMENT '按部门设定授权范围',
  `ROLE` mediumtext NOT NULL COMMENT '按角色设定授权范围',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8 COMMENT='BPM管理权限';

-- 正在导出表  td_oa_bak.bpm_priv 的数据：~1 rows (大约)
INSERT INTO `bpm_priv` (`ID`, `FLOW_ID`, `PRIV_TYPE`, `PRIV_SCOPE`, `USER`, `DEPT`, `ROLE`, `CORP_ID`) VALUES
	(1, 1, 1, 'ALL_DEPT', '1,', '', '', '');

-- 导出  表 td_oa_bak.bpm_process 结构
DROP TABLE IF EXISTS `bpm_process`;
CREATE TABLE IF NOT EXISTS `bpm_process` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程ID',
  `PRCS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '步骤ID',
  `PRCS_TYPE` tinyint(4) NOT NULL DEFAULT '0' COMMENT '节点类型(0-步骤节点,1-自流程节点,2-外部流转节点)',
  `PRCS_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '步骤名称',
  `PRCS_USER` mediumtext COMMENT '经办人ID串',
  `PRCS_ITEM` mediumtext COMMENT '可写字段串',
  `HIDDEN_ITEM` mediumtext COMMENT '保密字段串',
  `REQUIRED_ITEM` mediumtext COMMENT '必填字段串',
  `PRCS_DEPT` mediumtext COMMENT '经办部门ID串',
  `PRCS_PRIV` mediumtext COMMENT '经办角色ID串',
  `PRCS_TO` mediumtext COMMENT '转交步骤ID串',
  `SET_LEFT` int(11) NOT NULL DEFAULT '0' COMMENT '节点横坐标',
  `SET_TOP` int(11) NOT NULL DEFAULT '0' COMMENT '节点纵坐标',
  `PLUGIN` mediumtext NOT NULL COMMENT '转交调用插件',
  `PLUGIN_SAVE` mediumtext NOT NULL COMMENT '保存调用插件',
  `PRCS_ITEM_AUTO` mediumtext NOT NULL COMMENT '允许在不可写情况下自动赋值的宏控件',
  `PRCS_IN` mediumtext NOT NULL COMMENT '转入条件组成的串',
  `PRCS_OUT` mediumtext NOT NULL COMMENT '转出条件组成的串',
  `FEEDBACK` varchar(20) NOT NULL DEFAULT '0' COMMENT '是否允许会签(0-允许会签,1-禁止会签,2-强制会签)',
  `PRCS_IN_SET` mediumtext NOT NULL COMMENT '转入条件组成的逻辑表达式',
  `PRCS_OUT_SET` mediumtext NOT NULL COMMENT '转出条件组成的逻辑表达式',
  `AUTO_TYPE` varchar(20) NOT NULL DEFAULT '',
  `AUTO_DEPT` mediumtext NOT NULL COMMENT '自动选择指定自动选择的部门',
  `AUTO_USER_OP` mediumtext NOT NULL COMMENT '指定自动选择默认人员—主办人',
  `AUTO_USER` mediumtext NOT NULL COMMENT '指定自动选择默认人员—经办人',
  `AUTO_USER_OP_RETURN` mediumtext NOT NULL COMMENT '指定子流程返回父流程时自动选择默认人员—主办人',
  `AUTO_USER_RETURN` mediumtext NOT NULL COMMENT '指定子流程返回父流程时自动选择默认人员—经办人',
  `USER_FILTER` varchar(20) NOT NULL DEFAULT '' COMMENT '选人过滤规则(1-只允许选择本部门经办人,2-只允许选择本角色经办人,3-只允许选择上级部门经办人,4-只允许选择下级部门经办人,)',
  `USER_FILTER_PRCS_PRIV` mediumtext COMMENT '选人过滤规则指定角色',
  `USER_FILTER_PRCS_PRIV_OTHER` mediumtext COMMENT '选人过滤规则指定辅助角色',
  `USER_FILTER_PRCS_DEPT` mediumtext COMMENT '选人过滤规则指定部门',
  `USER_FILTER_PRCS_DEPT_OTHER` mediumtext COMMENT '选人过滤规则指定辅助部门',
  `TIME_OUT` varchar(20) NOT NULL DEFAULT '' COMMENT '办理时限',
  `TIME_OUT_MODIFY` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否允许转交时设置办理时限(0-不允许,1-允许)',
  `TIME_OUT_ATTEND` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否排除非工作时段(按排班类型)：(0-否,1-是,)',
  `SIGNLOOK` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '会签意见可见性(0-总是可见,1-本步骤经办人之间不可见,2-针对其他步骤不可见,)',
  `TOP_DEFAULT` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '主办人相关选项：(0-明确指定主办人,1-先接收者为主办人,2-无主办人会签,)',
  `USER_LOCK` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否允许修改主办人相关选项及默认经办人：(0-不允许,1-允许,)',
  `MAIL_TO` mediumtext NOT NULL COMMENT '转交时内部邮件通知以下人员ID串',
  `MAIL_TO_DEPT` mediumtext NOT NULL COMMENT '转交时内部邮件通知以下部门人员ID串',
  `MAIL_TO_PRIV` mediumtext NOT NULL COMMENT '转交时内部邮件通知以下角色人员ID串',
  `SYNC_DEAL` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否允许并发(0-禁止并发,1-允许并发,2-强制并发)',
  `SYNC_DEAL_CHECK` text NOT NULL COMMENT '用途未知',
  `TURN_PRIV` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '强制转交，经办人未办理完毕时是否允许主办人强制转交(0-不允许,1-允许,)',
  `CHILD_FLOW` int(11) NOT NULL DEFAULT '0' COMMENT '子流程的流程ID',
  `GATHER_NODE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '并发合并选项(0-非强制合并,1-强制合并,)',
  `GATHER_SET` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开启强制合并后的设置项(0-每条分支到达都会触发后续操作,1-全部分支合并后触发后续操作)',
  `ALLOW_BACK` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否允许回退(0-不允许,1-允许回退上一步骤,2-允许回退之前步骤,)',
  `ATTACH_PRIV` varchar(10) NOT NULL DEFAULT '1,2,3,4,' COMMENT '公共附件中的Office文档详细权限设置：(1-新建权限,2-编辑权限,3-删除权限,4-下载权限,5-打印权限,)',
  `AUTO_BASE_USER` int(11) NOT NULL DEFAULT '0' COMMENT '部门针对对象步骤的ID，0为当前步骤。配合自动选人规则使用。当自动选人规则为以下选项时启用(2-自动选择本部门主管,4-自动选择上级主管领导,6-自动选择上级分管领导,9-自动选择本部门助理,',
  `CONDITION_DESC` mediumtext NOT NULL COMMENT '不符合条件公式时，给用户的文字描述',
  `RELATION_IN` mediumtext NOT NULL COMMENT '父流程->子流程映射关系',
  `RELATION_OUT` mediumtext NOT NULL COMMENT '子流程->父流程映射关系',
  `REMIND_FLAG` int(11) NOT NULL COMMENT '用途未知',
  `DISP_AIP` int(11) NOT NULL DEFAULT '0' COMMENT '对应呈批单(0-表示不启用呈批单)',
  `TIME_OUT_TYPE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '超时计算方法(0-本步骤接收后开始计时,1-上一步骤转交后开始计时,)',
  `ATTACH_EDIT_PRIV` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否允许本步骤经办人编辑附件(0-不允许,1-允许,)',
  `ATTACH_EDIT_PRIV_ONLINE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否允许本步骤经办人在线创建文档(0-允许,1-不允许,)',
  `ATTACH_MACRO_MARK` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '宏标记附件上传为图片时展示效果(0-显示图片,1-显示图标和名称,)',
  `CONTROL_MODE` mediumtext COMMENT '列表控件模式(1-修改模式,2-添加模式,3-删除模式,保存格式如下例：列表控件1,列表控件2,|1`2`3,1`2,)',
  `LIST_COLUMN_PRIV` mediumtext COMMENT '列表控件单独列权限(1-只读,2-保密,4-必填，各项的与值)',
  `VIEW_PRIV` int(1) DEFAULT NULL COMMENT '传阅设置(0-不允许,1-允许,)',
  `FILEUPLOAD_PRIV` mediumtext NOT NULL COMMENT '附件上传控件的权限(1-新建,2-编辑,3-删除,4-下载,5-打印)',
  `IMGUPLOAD_PRIV` mediumtext NOT NULL COMMENT '图片上传控件的权限(1-新建,2-删除,3-下载)',
  `SIGN_TYPE` tinyint(3) NOT NULL DEFAULT '1' COMMENT '会签人设置(0-不允许,1-本步骤经办人,2-全部人员)',
  `COUNTERSIGN` tinyint(3) NOT NULL DEFAULT '0' COMMENT '会签人加签(0-不允许，1-允许)',
  `UI_TYPE` varchar(6) DEFAULT NULL COMMENT '人机交互方式(auto-自动排版,form-自定义表单,iframe-嵌入第三方页面,url-引用第三方页面)',
  `UI_URL` varchar(255) DEFAULT NULL COMMENT '人机交互地址信息',
  `UI_DESC` varchar(200) DEFAULT NULL COMMENT '人机交互描述信息',
  `AUTO_PRIVE` mediumtext NOT NULL COMMENT '智能选人指定角色',
  `OVER_FLOW` tinyint(3) NOT NULL COMMENT '是否结束流程(0-否,1-是)',
  `BACKFLOW` tinyint(3) NOT NULL COMMENT '是否重新走流程(0-是,1-否)',
  `IS_MYSELF` tinyint(3) NOT NULL COMMENT '当前步骤的主办人和下一步骤主办人相同时,是否自动转交下一步(0-否,1-是)',
  `ALLOW_NEXT_WORK` tinyint(3) NOT NULL COMMENT '按部门或角色设置时,其没有人员是否允许自动选择下一步骤(0-否,1-是)',
  `WORKINGDAYS_TYPE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '工作天数换算方式(0-24小时为一天，1-按个人排班类型工作时长为一天)',
  `UI_EMBE` tinyint(3) DEFAULT '0' COMMENT '人机交互是否嵌入模块页面',
  `ADD_ATTACH` tinyint(3) NOT NULL DEFAULT '0' COMMENT '父子流程是否拷贝公共附件(0-否，1-是)',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `MAP_IN_EXT` mediumtext COMMENT '变量映射关系',
  `FLOW_LIST_EXT` int(10) DEFAULT NULL COMMENT '外部流程',
  `EXT_ACTION` int(4) DEFAULT NULL COMMENT '外部操作',
  `EXT_ID` int(10) DEFAULT NULL,
  `COPY_ATTACH` int(2) DEFAULT '0' COMMENT '是否COPY公共附件',
  `UI_FORM_ID` int(11) DEFAULT '0' COMMENT '表单ID',
  `UI_LIST_NO` int(4) DEFAULT NULL COMMENT '排版列数（1-5）',
  `BACKWAY` tinyint(3) NOT NULL DEFAULT '0' COMMENT '退回步骤的方式',
  `ALLOW_CHOOSE_PRCS` tinyint(3) DEFAULT NULL COMMENT '允许并发时选择此步骤;0-是，1-否，2-禁止取消',
  `NEW_NAME` mediumtext COMMENT '转交 ，退回，保存，办理完毕,增加会签人（新名称）',
  `PLACE_FILE` tinyint(3) NOT NULL DEFAULT '1' COMMENT '此步骤是否设置归档',
  `MAP_FILE` mediumtext COMMENT '归档和流程的映射字段',
  `TIME_OUT_TURNWORK` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否允许超时自动转交(0-不允许,1-允许)',
  `SIGNACTION` varchar(10) NOT NULL DEFAULT '1,' COMMENT '经办人和主办人强制会签',
  `PRCS_TO_LIMIT` mediumtext COMMENT '超时转交的步骤',
  `OCR_PRIV` text NOT NULL COMMENT 'ocr控件的权限',
  `COUNTERSIGN_COLUMN_PRIV` mediumtext NOT NULL COMMENT '会签必填权限控制',
  `CONTRACT_LOCK` varchar(100) NOT NULL DEFAULT '' COMMENT '[start:起草合同,check:审阅合同,sign:合同盖章]',
  `VIEW_CONFIG` text NOT NULL COMMENT '步骤传阅设置信息',
  `MOBILE_GROUP_FIELD` text NOT NULL COMMENT '组容器参数配置',
  `PRCS_SWITCH_INTEGRATE` text NOT NULL COMMENT '流程步骤开关整合，存放JSON字符串',
  `ORDER_EXECUTE` tinyint(3) DEFAULT '0' COMMENT '顺序执行规则设置',
  `AUTO_DEPT_CUSTOM_USER` varchar(50) NOT NULL DEFAULT '' COMMENT '部门自定义字段自动选人',
  `AUTO_TYPE_RETURN` varchar(20) NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `FLOW_ID` (`FLOW_ID`),
  KEY `FORM_ID` (`PRCS_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=444 DEFAULT CHARSET=utf8 COMMENT='BPM流程步骤定义';

-- 正在导出表  td_oa_bak.bpm_process 的数据：~1 rows (大约)
INSERT INTO `bpm_process` (`ID`, `FLOW_ID`, `PRCS_ID`, `PRCS_TYPE`, `PRCS_NAME`, `PRCS_USER`, `PRCS_ITEM`, `HIDDEN_ITEM`, `REQUIRED_ITEM`, `PRCS_DEPT`, `PRCS_PRIV`, `PRCS_TO`, `SET_LEFT`, `SET_TOP`, `PLUGIN`, `PLUGIN_SAVE`, `PRCS_ITEM_AUTO`, `PRCS_IN`, `PRCS_OUT`, `FEEDBACK`, `PRCS_IN_SET`, `PRCS_OUT_SET`, `AUTO_TYPE`, `AUTO_DEPT`, `AUTO_USER_OP`, `AUTO_USER`, `AUTO_USER_OP_RETURN`, `AUTO_USER_RETURN`, `USER_FILTER`, `USER_FILTER_PRCS_PRIV`, `USER_FILTER_PRCS_PRIV_OTHER`, `USER_FILTER_PRCS_DEPT`, `USER_FILTER_PRCS_DEPT_OTHER`, `TIME_OUT`, `TIME_OUT_MODIFY`, `TIME_OUT_ATTEND`, `SIGNLOOK`, `TOP_DEFAULT`, `USER_LOCK`, `MAIL_TO`, `MAIL_TO_DEPT`, `MAIL_TO_PRIV`, `SYNC_DEAL`, `SYNC_DEAL_CHECK`, `TURN_PRIV`, `CHILD_FLOW`, `GATHER_NODE`, `GATHER_SET`, `ALLOW_BACK`, `ATTACH_PRIV`, `AUTO_BASE_USER`, `CONDITION_DESC`, `RELATION_IN`, `RELATION_OUT`, `REMIND_FLAG`, `DISP_AIP`, `TIME_OUT_TYPE`, `ATTACH_EDIT_PRIV`, `ATTACH_EDIT_PRIV_ONLINE`, `ATTACH_MACRO_MARK`, `CONTROL_MODE`, `LIST_COLUMN_PRIV`, `VIEW_PRIV`, `FILEUPLOAD_PRIV`, `IMGUPLOAD_PRIV`, `SIGN_TYPE`, `COUNTERSIGN`, `UI_TYPE`, `UI_URL`, `UI_DESC`, `AUTO_PRIVE`, `OVER_FLOW`, `BACKFLOW`, `IS_MYSELF`, `ALLOW_NEXT_WORK`, `WORKINGDAYS_TYPE`, `UI_EMBE`, `ADD_ATTACH`, `CORP_ID`, `MAP_IN_EXT`, `FLOW_LIST_EXT`, `EXT_ACTION`, `EXT_ID`, `COPY_ATTACH`, `UI_FORM_ID`, `UI_LIST_NO`, `BACKWAY`, `ALLOW_CHOOSE_PRCS`, `NEW_NAME`, `PLACE_FILE`, `MAP_FILE`, `TIME_OUT_TURNWORK`, `SIGNACTION`, `PRCS_TO_LIMIT`, `OCR_PRIV`, `COUNTERSIGN_COLUMN_PRIV`, `CONTRACT_LOCK`, `VIEW_CONFIG`, `MOBILE_GROUP_FIELD`, `PRCS_SWITCH_INTEGRATE`, `ORDER_EXECUTE`, `AUTO_DEPT_CUSTOM_USER`, `AUTO_TYPE_RETURN`) VALUES
	(46, 6, 1, 0, '员工请假申请', '', 'E46845026C3B8C6127FA9B04F64904D6,E5D0E4C6F07C687F932EDCBD51BAF2B8,50D721413EE485FEB9AF257765358357,57C429D16EF06A31F2E1BC2E0B463E89,E44168C1781D4F47271A698CDE0D5DD8,B3759F8A918780BE7A6B230D9BE33838,E792FBB2078FBC99831CEDF704616135,8F17C2A356E95CDA4A8A45634A551041,[A@],', '', ',', 'ALL_DEPT', '', '4,3,2,', 14, 156, '', '', '申请人,申请人所在部门,登记时间,', '', '', '0', '', '', '', '', '', '', '', '', '', '', '', '', '', '3', 0, 0, 0, 0, 1, '', '', '', 0, '', 0, 0, 0, 0, 0, '1,2,3,4,', 0, '\n', '', '', 0, 0, 0, 0, 0, 0, '', '', 0, 'a:1:{s:9:"FILEPRIV_";a:0:{}}', 'a:1:{s:9:"IMG_PRIV_";a:0:{}}', 1, 0, 'form', '', '', '', 0, 0, 0, 0, 0, 0, 0, '', '', 0, 0, NULL, 0, 6, NULL, 0, 0, '{"NEXT_WORK":"","ONE_KEY":"","NAME_WORK":"","OVER_WORK":"","RETURN_WORK":"","SAVE_WORK":"","PUBLIC_WORK":"","SIGN_WORK":""}', 1, '', 0, '1,', '', 'a:1:{s:9:"OCR_PRIV_";a:0:{}}', '', '', '', '', '', 0, '', '');

-- 导出  表 td_oa_bak.bpm_query_tpl 结构
DROP TABLE IF EXISTS `bpm_query_tpl`;
CREATE TABLE IF NOT EXISTS `bpm_query_tpl` (
  `SEQ_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `TPL_NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '模板名称',
  `USER_ID` varchar(100) NOT NULL DEFAULT '' COMMENT '用户ID',
  `FLOW_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程ID',
  `VIEW_EXT_FIELDS` mediumtext COMMENT '扩展显示字段',
  `GROUP_BY_FIELDS` mediumtext COMMENT '分组字段',
  `DATA_CONDITIONS` mediumtext COMMENT '表单数据过滤条件',
  `FLOW_CONDITIONS` mediumtext COMMENT '流程过滤条件',
  `DATA_XML` mediumtext NOT NULL COMMENT '查询模板内容',
  `COND_FORMULA` varchar(200) NOT NULL COMMENT '查询条件公式',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `SEARCH_COLUMN_CONDITION` text NOT NULL COMMENT '展示数据界面的查询字段条件',
  `USER_PRIV` text NOT NULL COMMENT '用户视图分享权限',
  `CALCULATE_FIELD` mediumtext NOT NULL COMMENT '统计字段',
  `MENU` text NOT NULL COMMENT '菜单设置',
  PRIMARY KEY (`SEQ_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程查询模板';

-- 正在导出表  td_oa_bak.bpm_query_tpl 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_rule 结构
DROP TABLE IF EXISTS `bpm_rule`;
CREATE TABLE IF NOT EXISTS `bpm_rule` (
  `RULE_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程ID',
  `USER_ID` varchar(40) NOT NULL COMMENT '委托人ID',
  `TO_ID` varchar(40) NOT NULL COMMENT '被委托人ID',
  `BEGIN_DATE` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `END_DATE` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `STATUS` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(0-关闭,1-开启)',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `CREATE_USER` varchar(20) DEFAULT NULL COMMENT '创建人',
  `UPDATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `UPDATE_USER` varchar(20) DEFAULT NULL COMMENT '最后修改人',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`RULE_ID`),
  KEY `FLOW_ID` (`FLOW_ID`),
  KEY `USER_ID` (`USER_ID`),
  KEY `BEGIN_DATE` (`BEGIN_DATE`),
  KEY `END_DATE` (`END_DATE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM工作委托';

-- 正在导出表  td_oa_bak.bpm_rule 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run 结构
DROP TABLE IF EXISTS `bpm_run`;
CREATE TABLE IF NOT EXISTS `bpm_run` (
  `RUN_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '流程实例ID',
  `RUN_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '流程实例名称',
  `FLOW_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程ID',
  `BEGIN_USER` varchar(40) NOT NULL COMMENT '流程发起人ID',
  `BEGIN_DEPT` int(11) NOT NULL COMMENT '流程发起人部门ID',
  `BEGIN_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `end_time` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `ATTACHMENT_ID` mediumtext NOT NULL COMMENT '附件ID串',
  `ATTACHMENT_NAME` mediumtext NOT NULL COMMENT '附件名称串',
  `DEL_FLAG` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)删除后流程实例可在工作销毁中确实删除或还原',
  `FOCUS_USER` mediumtext NOT NULL COMMENT '关注该流程的用户',
  `PARENT_RUN` int(11) NOT NULL DEFAULT '0' COMMENT '父流程ID',
  `FROM_USER` varchar(20) NOT NULL COMMENT '已废弃，改为bpm_run_PRCS表中记录',
  `AIP_FILES` mediumtext NOT NULL COMMENT '相关的版式文件信息',
  `PRE_SET` mediumtext NOT NULL COMMENT '待定',
  `VIEW_USER` mediumtext NOT NULL COMMENT '传阅人ID串工作办理结束时选择的传阅人',
  `VIEW_CONTENTS` varchar(10) DEFAULT '''1,2,3,4''' COMMENT '用户转交时选择的传阅内容。1-表单2-公共附件3-会签意见4-流程图5-查阅信息7-二维码',
  `ARCHIVE` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否归档(0-否,1-是)',
  `FORCE_OVER` mediumtext NOT NULL COMMENT '强制结束信息',
  `work_level` int(11) NOT NULL DEFAULT '0' COMMENT '工作等级 0-普通 1-重要 2-紧急',
  `DEL_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `MODULE_NAME` varchar(40) DEFAULT NULL COMMENT '用于区分业务流程平台和其他模块',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `IS_EXTERNAL` tinyint(4) DEFAULT '0' COMMENT '判断流程是否是外部新建',
  `VERSION_ID` int(11) NOT NULL DEFAULT '0' COMMENT '表单版本',
  `VERSION_LOG` tinyint(3) NOT NULL DEFAULT '0' COMMENT '表单版本标识(1为有版本控制，0为无版本控制)',
  `SECRET_LEVEL` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '密级',
  `REPLACE_TEMPLATE` text NOT NULL COMMENT '工作名称文号表单数据替换模板',
  PRIMARY KEY (`RUN_ID`),
  KEY `FLOW_ID` (`RUN_ID`),
  KEY `FORM_ID` (`FLOW_ID`),
  KEY `RUN_NAME` (`RUN_NAME`),
  KEY `BEGIN_TIME` (`BEGIN_TIME`),
  KEY `END_TIME` (`end_time`),
  KEY `USER_AND_RUN` (`BEGIN_USER`,`RUN_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8 COMMENT='BPM工作基本信息';

-- 正在导出表  td_oa_bak.bpm_run 的数据：~1 rows (大约)
INSERT INTO `bpm_run` (`RUN_ID`, `RUN_NAME`, `FLOW_ID`, `BEGIN_USER`, `BEGIN_DEPT`, `BEGIN_TIME`, `end_time`, `ATTACHMENT_ID`, `ATTACHMENT_NAME`, `DEL_FLAG`, `FOCUS_USER`, `PARENT_RUN`, `FROM_USER`, `AIP_FILES`, `PRE_SET`, `VIEW_USER`, `VIEW_CONTENTS`, `ARCHIVE`, `FORCE_OVER`, `work_level`, `DEL_TIME`, `MODULE_NAME`, `CORP_ID`, `IS_EXTERNAL`, `VERSION_ID`, `VERSION_LOG`, `SECRET_LEVEL`, `REPLACE_TEMPLATE`) VALUES
	(1, '出差审批(2024-07-01 13:44:48)', 32, '2', 1, '2024-07-01 13:44:48', '1000-01-01 00:00:00', '', '', 0, '', 0, '', '', '', '', '\'1,2,3,4\'', 0, '', 0, '1000-01-01 00:00:00', 'approve_center', '', 0, 0, 0, 0, '');

-- 导出  表 td_oa_bak.bpm_run_attach 结构
DROP TABLE IF EXISTS `bpm_run_attach`;
CREATE TABLE IF NOT EXISTS `bpm_run_attach` (
  `SEQ_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `RUN_ID` int(11) NOT NULL COMMENT '流程实例ID',
  `ATTACHMENT_ID` varchar(40) NOT NULL COMMENT '附件ID',
  `ATTACHMENT_NAME` varchar(255) NOT NULL COMMENT '附件名称',
  `FLOW_PRCS` int(11) NOT NULL COMMENT '步骤号',
  `UPLOAD_USER` varchar(40) NOT NULL COMMENT '上传人员',
  `UPLOAD_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`SEQ_ID`),
  KEY `RUN_ID` (`RUN_ID`,`ATTACHMENT_NAME`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='BPM工作附件信息';

-- 正在导出表  td_oa_bak.bpm_run_attach 的数据：~2 rows (大约)
INSERT INTO `bpm_run_attach` (`SEQ_ID`, `RUN_ID`, `ATTACHMENT_ID`, `ATTACHMENT_NAME`, `FLOW_PRCS`, `UPLOAD_USER`, `UPLOAD_TIME`, `CORP_ID`) VALUES
	(1, 61, '504@2407_1384302268', '住宿费.pdf', 1, 'admin', '2024-07-05 09:57:21', ''),
	(2, 66, '508@2407_1282476887', '住宿费.jpg', 1, '6', '2024-07-05 10:32:28', '');

-- 导出  表 td_oa_bak.bpm_run_contact 结构
DROP TABLE IF EXISTS `bpm_run_contact`;
CREATE TABLE IF NOT EXISTS `bpm_run_contact` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `run_id` int(10) DEFAULT '0' COMMENT '流水号',
  `flow_prcs` int(11) DEFAULT '0' COMMENT '设计步骤',
  `prcs_id` int(11) DEFAULT '0' COMMENT '实际步骤',
  `contact_time` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `contact_user` varchar(40) DEFAULT '' COMMENT '流程关联上传者',
  `contact_run_id` int(10) DEFAULT '0' COMMENT '关联流水号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程关联';

-- 正在导出表  td_oa_bak.bpm_run_contact 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run_external 结构
DROP TABLE IF EXISTS `bpm_run_external`;
CREATE TABLE IF NOT EXISTS `bpm_run_external` (
  `id_ext` int(10) NOT NULL DEFAULT '0',
  `flow_id` int(10) NOT NULL DEFAULT '0',
  `flow_id_ext` int(10) NOT NULL DEFAULT '0',
  `run_id` int(10) NOT NULL DEFAULT '0',
  `run_id_ext` int(10) NOT NULL DEFAULT '0',
  `prcs_id_ext` int(10) NOT NULL DEFAULT '0',
  `flow_prcs_ext` int(10) NOT NULL DEFAULT '0',
  `send_id` int(10) NOT NULL DEFAULT '0',
  `receive_id` int(2) NOT NULL DEFAULT '0',
  `ext_flag` int(2) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id_ext`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 正在导出表  td_oa_bak.bpm_run_external 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run_feedback 结构
DROP TABLE IF EXISTS `bpm_run_feedback`;
CREATE TABLE IF NOT EXISTS `bpm_run_feedback` (
  `FEED_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `RUN_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例ID',
  `PRCS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例步骤ID[实际步骤号]',
  `FLOW_PRCS` int(11) NOT NULL COMMENT '流程步骤号[设计流程中的步骤号]',
  `USER_ID` varchar(40) NOT NULL COMMENT '用户ID',
  `CONTENT` longtext NOT NULL COMMENT '会签意见内容',
  `ATTACHMENT_ID` mediumtext NOT NULL COMMENT '附件ID串',
  `ATTACHMENT_NAME` mediumtext NOT NULL COMMENT '附件名称串',
  `EDIT_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `FEED_FLAG` tinyint(3) NOT NULL COMMENT '会签意见类型(1-点评意见,2-退回意见,3-加签意见)',
  `SIGN_DATA` mediumtext NOT NULL COMMENT '手写签批意见',
  `REPLY_ID` int(11) NOT NULL COMMENT '回复的是哪条意见ID',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `ADD_SIGN_USER` text NOT NULL COMMENT '记录增加会签人理由',
  PRIMARY KEY (`FEED_ID`),
  KEY `RUN_ID` (`RUN_ID`),
  KEY `PRCS_ID` (`PRCS_ID`),
  KEY `REPLY_ID` (`REPLY_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='BPM会签意见';

-- 正在导出表  td_oa_bak.bpm_run_feedback 的数据：~1 rows (大约)
INSERT INTO `bpm_run_feedback` (`FEED_ID`, `RUN_ID`, `PRCS_ID`, `FLOW_PRCS`, `USER_ID`, `CONTENT`, `ATTACHMENT_ID`, `ATTACHMENT_NAME`, `EDIT_TIME`, `FEED_FLAG`, `SIGN_DATA`, `REPLY_ID`, `CORP_ID`, `ADD_SIGN_USER`) VALUES
	(1, 13, 2, 2, '5', '<p>同意</p>', '', '', '2024-07-02 09:36:29', 0, '', 0, '', '');

-- 导出  表 td_oa_bak.bpm_run_hook 结构
DROP TABLE IF EXISTS `bpm_run_hook`;
CREATE TABLE IF NOT EXISTS `bpm_run_hook` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `run_id` int(11) NOT NULL COMMENT '流程实例ID',
  `module` varchar(40) NOT NULL COMMENT '映射业务模块',
  `field` varchar(40) NOT NULL COMMENT '映射字段',
  `key_id` int(11) NOT NULL COMMENT '映射字段的数据',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `HID` int(11) NOT NULL DEFAULT '0' COMMENT '业务引擎设置id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM工作与业务引擎数据映射';

-- 正在导出表  td_oa_bak.bpm_run_hook 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run_log 结构
DROP TABLE IF EXISTS `bpm_run_log`;
CREATE TABLE IF NOT EXISTS `bpm_run_log` (
  `LOG_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `RUN_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例ID',
  `RUN_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '流程实例名称',
  `FLOW_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程ID',
  `PRCS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例步骤ID[流程实例实际步骤号]',
  `FLOW_PRCS` int(11) NOT NULL DEFAULT '0' COMMENT '流程步骤ID[设计流程步骤号]',
  `USER_ID` varchar(40) NOT NULL COMMENT '操作人ID',
  `TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `IP` varchar(20) NOT NULL DEFAULT '' COMMENT '操作人IP地址',
  `TYPE` varchar(10) NOT NULL DEFAULT '' COMMENT '日志类型：(1-新建、转交、回退、收回,2-委托,3-删除,4-销毁,5-还原被终止的流程,6-编辑数据,7-流转过程中修改经办权限,)',
  `CONTENT` mediumtext NOT NULL COMMENT '日志信息',
  `UID` int(11) NOT NULL DEFAULT '0' COMMENT '用户UID',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`LOG_ID`),
  KEY `RUN_ID` (`RUN_ID`),
  KEY `USER_ID` (`USER_ID`),
  KEY `IP` (`IP`),
  KEY `RUN_NAME` (`RUN_NAME`),
  KEY `FLOW_ID` (`FLOW_ID`),
  KEY `TIME` (`TIME`),
  KEY `I_TYPE` (`TYPE`)
) ENGINE=InnoDB AUTO_INCREMENT=212 DEFAULT CHARSET=utf8 COMMENT='BPM工作日志';

-- 正在导出表  td_oa_bak.bpm_run_log 的数据：~1 rows (大约)
INSERT INTO `bpm_run_log` (`LOG_ID`, `RUN_ID`, `RUN_NAME`, `FLOW_ID`, `PRCS_ID`, `FLOW_PRCS`, `USER_ID`, `TIME`, `IP`, `TYPE`, `CONTENT`, `UID`, `CORP_ID`) VALUES
	(1, 1, '出差审批(2024-07-01 13:44:48)', 32, 1, 1, '2', '2024-07-01 13:44:49', '**************', '1', '新建工作出差审批(2024-07-01 13:44:48),办理人李佳', 0, '');

-- 导出  表 td_oa_bak.bpm_run_module_map 结构
DROP TABLE IF EXISTS `bpm_run_module_map`;
CREATE TABLE IF NOT EXISTS `bpm_run_module_map` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `run_id` int(10) unsigned NOT NULL DEFAULT '0',
  `flow_id` int(10) unsigned NOT NULL DEFAULT '0',
  `flow_prcs` int(10) unsigned NOT NULL DEFAULT '1',
  `module_key` varchar(2000) NOT NULL DEFAULT '',
  `corp_id` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`id`),
  KEY `run_id_flow_prcs` (`run_id`,`flow_prcs`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据关联表';

-- 正在导出表  td_oa_bak.bpm_run_module_map 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run_prcs 结构
DROP TABLE IF EXISTS `bpm_run_prcs`;
CREATE TABLE IF NOT EXISTS `bpm_run_prcs` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `RUN_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例ID',
  `PRCS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例步骤ID',
  `USER_ID` varchar(40) NOT NULL COMMENT '用户ID',
  `PRCS_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `DELIVER_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `PRCS_FLAG` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '步骤状态(1-未接收,2-办理中,3-转交下一步，下一步经办人无人接收,4-已办结,5-自由流程预设步骤,6-已挂起,)',
  `FLOW_PRCS` int(11) NOT NULL DEFAULT '0' COMMENT '步骤ID[设计流程中的步骤号]',
  `OP_FLAG` varchar(20) NOT NULL DEFAULT '1' COMMENT '是否主办(0-经办,1-主办)',
  `TOP_FLAG` varchar(20) NOT NULL DEFAULT '0' COMMENT '主办人选项(0-指定主办人,1-先接收者主办,2-无主办人会签,)',
  `PARENT` varchar(200) NOT NULL DEFAULT '0' COMMENT '上一步流程FLOW_PRCS',
  `CHILD_RUN` int(11) NOT NULL DEFAULT '0' COMMENT '子流程的流程实例ID',
  `TIME_OUT` varchar(20) NOT NULL DEFAULT '' COMMENT '待定',
  `FREE_ITEM` text NOT NULL COMMENT '步骤可写字段[仅自由流程且只有主办人生效]',
  `TIME_OUT_TEMP` varchar(50) NOT NULL DEFAULT '' COMMENT '待定',
  `OTHER_USER` mediumtext NOT NULL COMMENT '工作委托用户ID串',
  `TIME_OUT_FLAG` char(1) NOT NULL DEFAULT '0' COMMENT '是否超时(1-超时,其他-不超时)',
  `CREATE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `FROM_USER` varchar(20) NOT NULL DEFAULT '' COMMENT '工作移交用户ID',
  `ACTIVE_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `COMMENT` mediumtext NOT NULL COMMENT '批注',
  `PRCS_DEPT` int(11) NOT NULL COMMENT '超时统计查询增加部门',
  `PARENT_PRCS_ID` varchar(200) NOT NULL DEFAULT '0' COMMENT '上一步流程PRCS_ID',
  `PARENT_KEY_ID` text NOT NULL COMMENT '步骤来源ID',
  `BACK_PRCS_ID` int(11) NOT NULL DEFAULT '0' COMMENT '返回步骤PRCS_ID标志',
  `BACK_FLOW_PRCS` int(11) NOT NULL DEFAULT '0' COMMENT '返回步骤FLOW_PRCS标志',
  `TIME_OUT_ATTEND` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否排除工作时段按排班类型(0-否,1-是)',
  `TIME_OUT_TYPE` tinyint(2) NOT NULL DEFAULT '0' COMMENT '超时计算方法(0-本步骤接收后开始计时,1-上一步骤转交后开始计时 )',
  `RUN_PRCS_NAME` varchar(100) NOT NULL DEFAULT '',
  `RUN_PRCS_ID` varchar(50) NOT NULL DEFAULT '',
  `MOBILE_FLAG` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否是移动端操作(0-否,1-是)',
  `IS_REMIND` int(1) NOT NULL DEFAULT '0' COMMENT '是否催办',
  `TEMP_PRCS_FLAG` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '区分自由流程回收的是预设步骤还是普通流转步骤，1-普通流转步骤，5-预设步骤',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `PRCS_TYPE` tinyint(4) DEFAULT NULL COMMENT '节点类型',
  `PRCS_VERSION_ID` int(11) NOT NULL DEFAULT '0' COMMENT '步骤表单版本',
  `SIGN_ADD_CAUSE` mediumtext NOT NULL COMMENT '增加会签理由',
  `FREE_FORM_ITEM` text NOT NULL COMMENT '步骤按照表单字段选人[仅自由流程且只有主办人生效]',
  `FREE_PARENT` int(10) DEFAULT '0' COMMENT '自由流程父FLOW_PRCS',
  `FREE_PARENT_PRCS_ID` int(10) DEFAULT '0' COMMENT '自由流程父PRCS_ID',
  `VIEW_FLAG` int(2) DEFAULT '1' COMMENT '自由流程流程步骤是否显示',
  PRIMARY KEY (`ID`),
  KEY `I_PRCS_FLAG` (`PRCS_FLAG`),
  KEY `I_RUN_ID` (`RUN_ID`),
  KEY `I_USER_ID` (`USER_ID`),
  KEY `I_PRCS_ID` (`RUN_ID`,`PRCS_ID`),
  KEY `I_R_U_ID` (`RUN_ID`,`USER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=210 DEFAULT CHARSET=utf8 COMMENT='BPM工作步骤信息';

-- 正在导出表  td_oa_bak.bpm_run_prcs 的数据：~1 rows (大约)
INSERT INTO `bpm_run_prcs` (`ID`, `RUN_ID`, `PRCS_ID`, `USER_ID`, `PRCS_TIME`, `DELIVER_TIME`, `PRCS_FLAG`, `FLOW_PRCS`, `OP_FLAG`, `TOP_FLAG`, `PARENT`, `CHILD_RUN`, `TIME_OUT`, `FREE_ITEM`, `TIME_OUT_TEMP`, `OTHER_USER`, `TIME_OUT_FLAG`, `CREATE_TIME`, `FROM_USER`, `ACTIVE_TIME`, `COMMENT`, `PRCS_DEPT`, `PARENT_PRCS_ID`, `PARENT_KEY_ID`, `BACK_PRCS_ID`, `BACK_FLOW_PRCS`, `TIME_OUT_ATTEND`, `TIME_OUT_TYPE`, `RUN_PRCS_NAME`, `RUN_PRCS_ID`, `MOBILE_FLAG`, `IS_REMIND`, `TEMP_PRCS_FLAG`, `CORP_ID`, `PRCS_TYPE`, `PRCS_VERSION_ID`, `SIGN_ADD_CAUSE`, `FREE_FORM_ITEM`, `FREE_PARENT`, `FREE_PARENT_PRCS_ID`, `VIEW_FLAG`) VALUES
	(1, 1, 1, '2', '2024-07-01 13:44:49', '2024-07-01 13:50:15', 3, 1, '1', '0', '0', 0, '', '', '', '', '0', '2024-07-01 13:44:48', '', '1000-01-01 00:00:00', '', 1, '0', '', 0, 0, 0, 0, '', '', 0, 0, 0, '', NULL, 0, '', '', 0, 0, 1);

-- 导出  表 td_oa_bak.bpm_run_variable_data 结构
DROP TABLE IF EXISTS `bpm_run_variable_data`;
CREATE TABLE IF NOT EXISTS `bpm_run_variable_data` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `RUN_ID` int(11) NOT NULL DEFAULT '0' COMMENT '流程实例ID',
  `RUN_NAME` varchar(200) DEFAULT NULL COMMENT '流程实例名称',
  `BEGIN_USER` varchar(20) DEFAULT NULL COMMENT '工作创建者',
  `BEGIN_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `FLOW_AUTO_NUM` int(11) DEFAULT NULL COMMENT '文号流水',
  `BASIC_DATA` longtext COMMENT '除签章、二维码外的变量数据',
  `SEAL_DATA` longtext COMMENT '章签类数据',
  `QR_DATA` longtext COMMENT '二维码类数据',
  `PARA_DATA` mediumtext,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_runid` (`RUN_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM变量数据';

-- 正在导出表  td_oa_bak.bpm_run_variable_data 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_run_variable_list_data 结构
DROP TABLE IF EXISTS `bpm_run_variable_list_data`;
CREATE TABLE IF NOT EXISTS `bpm_run_variable_list_data` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `RUN_ID` int(11) NOT NULL COMMENT '流程实例ID',
  `TABLE_ID` int(11) NOT NULL COMMENT '所属变量分类ID',
  `DATA` longtext COMMENT '明细数据（行列构成的二维数组）',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx` (`RUN_ID`,`TABLE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM明细变量数据';

-- 正在导出表  td_oa_bak.bpm_run_variable_list_data 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_sort 结构
DROP TABLE IF EXISTS `bpm_sort`;
CREATE TABLE IF NOT EXISTS `bpm_sort` (
  `SORT_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `SORT_NO` int(11) NOT NULL DEFAULT '0' COMMENT '排序号',
  `SORT_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '分类名称',
  `DEPT_ID` int(11) NOT NULL DEFAULT '0' COMMENT '所属部门ID',
  `SORT_PARENT` int(11) NOT NULL DEFAULT '0' COMMENT '上级分类ID',
  `HAVE_CHILD` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否有子分类(1-是,其他-否)',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `MODEL_ID` varchar(40) DEFAULT '' COMMENT '模块ID',
  PRIMARY KEY (`SORT_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 COMMENT='BPM分类';

-- 正在导出表  td_oa_bak.bpm_sort 的数据：~1 rows (大约)
INSERT INTO `bpm_sort` (`SORT_ID`, `SORT_NO`, `SORT_NAME`, `DEPT_ID`, `SORT_PARENT`, `HAVE_CHILD`, `CORP_ID`, `MODEL_ID`) VALUES
	(2, 1, '行政管理', 0, 0, 0, '', '');

-- 导出  表 td_oa_bak.bpm_timer 结构
DROP TABLE IF EXISTS `bpm_timer`;
CREATE TABLE IF NOT EXISTS `bpm_timer` (
  `TID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_ID` int(11) NOT NULL COMMENT '流程ID',
  `USER_STR` mediumtext NOT NULL COMMENT '发起人ID串',
  `DEPT_STR` mediumtext COMMENT '定时任务发起部门串',
  `PRIV_STR` mediumtext COMMENT '定时任务发起角色串',
  `TYPE` varchar(1) NOT NULL COMMENT '提醒类型(1-仅此一次,2-按日,3-按周,4-按月,5-按年,)',
  `REMIND_DATE` varchar(10) NOT NULL COMMENT '提醒日期(1-仅此一次，存具体日期,2-按日，为空,3-按周，存星期几,4-按月，存每月几号,5-按年，存每年几月几号,)',
  `REMIND_TIME` time NOT NULL COMMENT '提醒时间',
  `LAST_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`TID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM定时设置';

-- 正在导出表  td_oa_bak.bpm_timer 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_trigger 结构
DROP TABLE IF EXISTS `bpm_trigger`;
CREATE TABLE IF NOT EXISTS `bpm_trigger` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `NAME` varchar(255) DEFAULT NULL COMMENT '触发器名称',
  `FLOW_ID` int(11) unsigned NOT NULL COMMENT '流程ID',
  `FLOW_PRCS` int(11) unsigned NOT NULL COMMENT '步骤ID',
  `PLUGIN_TYPE` varchar(32) NOT NULL COMMENT '触发节点',
  `PLUGIN_WAY` varchar(32) NOT NULL COMMENT '执行方式',
  `PLUGIN` varchar(32) NOT NULL COMMENT '已启用的插件，新插件为文件夹名，老插件为文件名',
  `ACTIVED` tinyint(3) unsigned NOT NULL COMMENT '是否已启用：0未启用，1已启用',
  `SORT_ID` int(11) unsigned NOT NULL COMMENT '排序号',
  `DESCRIPTION` varchar(255) DEFAULT NULL COMMENT '描述',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM触发器表';

-- 正在导出表  td_oa_bak.bpm_trigger 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_type 结构
DROP TABLE IF EXISTS `bpm_type`;
CREATE TABLE IF NOT EXISTS `bpm_type` (
  `FLOW_ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FLOW_NAME` varchar(200) NOT NULL DEFAULT '' COMMENT '流程名称',
  `FORM_ID` int(11) NOT NULL DEFAULT '0' COMMENT '表单ID',
  `FLOW_DOC` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否允许附件(0-否,1-是)',
  `FLOW_TYPE` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '流程类型(1-固定流程,2-自由流程)',
  `MANAGE_USER` mediumtext NOT NULL COMMENT '管理与监控权限[全局]-已废弃',
  `FLOW_NO` int(11) NOT NULL DEFAULT '1' COMMENT '流程排序号',
  `FLOW_SORT` int(11) NOT NULL DEFAULT '0' COMMENT '流程分类ID',
  `AUTO_NAME` mediumtext NOT NULL COMMENT '自动文号表达式',
  `AUTO_NUM` int(11) NOT NULL DEFAULT '0' COMMENT '自动编号计数器',
  `AUTO_LEN` int(11) NOT NULL DEFAULT '0' COMMENT '自动编号显示长度',
  `QUERY_USER` mediumtext NOT NULL COMMENT '查询权限[全局]-已废弃',
  `FLOW_DESC` mediumtext NOT NULL COMMENT '流程说明',
  `AUTO_EDIT` varchar(20) NOT NULL DEFAULT '1' COMMENT '新建工作时是否允许手工修改文号：(0-不允许手工修改文号,1-允许手工修改文号,2-允许在自动文号前输入前缀,3-允许在自动文号后输入后缀,4-允许在自动文号前后输入前缀和后缀,)',
  `NEW_USER` mediumtext NOT NULL COMMENT '自由流程新建权限：分为按用户、按部门、按角色三种授权方式,形成“用户ID串|部门ID串|角色ID串”格式的字符串,其中用户ID串、部门ID串和角色ID串均是逗号分隔的字符串,',
  `QUERY_ITEM` mediumtext NOT NULL COMMENT '查询字段串',
  `COMMENT_PRIV` char(1) NOT NULL DEFAULT '3' COMMENT '点评权限-已废弃',
  `DEPT_ID` int(11) NOT NULL DEFAULT '0' COMMENT '所属部门ID',
  `FREE_PRESET` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否允许预设步骤(0-否,1-是)',
  `FREE_OTHER` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT '委托类型(0-禁止委托,1-仅允许委托当前步骤经办人(本步骤实际的经办人，该步骤可能定义了五个人，但是转交时选择了三个),2-自由委托,3-按步骤设置的经办权限委托(跟1的区别是按照定义的经办人来委托))',
  `QUERY_USER_DEPT` mediumtext NOT NULL COMMENT '本部门发起查询权限-已废弃',
  `MANAGE_USER_DEPT` mediumtext NOT NULL COMMENT '本部门管理与监控权限-已废弃',
  `EDIT_PRIV` mediumtext NOT NULL COMMENT '编辑权限-已废弃',
  `LIST_FLDS_STR` mediumtext NOT NULL COMMENT '列表扩展字段串查询页面仅查询该流程时生效',
  `ALLOW_PRE_SET` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '待定',
  `FORCE_PRE_SET` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否强制修改文号(1-是,其他-否),新建工作时是否允许手工修改文号为(2-允许在自动文号前输入前缀,3-允许在自动文号后输入后缀,4-允许在自动文号前后输入前缀和后缀,时可设置该选项)',
  `MODEL_ID` mediumtext NOT NULL COMMENT '流程对应模块ID',
  `MODEL_NAME` mediumtext NOT NULL COMMENT '流程对应模块名称',
  `ATTACHMENT_ID` mediumtext NOT NULL COMMENT '说明文档附件ID串',
  `ATTACHMENT_NAME` mediumtext NOT NULL COMMENT '说明文档附件名称串',
  `VIEW_USER` mediumtext COMMENT '传阅人ID串',
  `VIEW_DEPT` mediumtext COMMENT '传阅部门ID串',
  `VIEW_ROLE` mediumtext COMMENT '传阅角色ID串',
  `VIEW_PRIV` int(1) DEFAULT NULL COMMENT '允许传阅(0-不允许,1-允许)',
  `IS_VERSION` int(1) NOT NULL COMMENT '是否启用版本控制(0-否,1-是)',
  `FLOW_ACTION` varchar(10) DEFAULT NULL COMMENT '更多选项(1公告通知,2内部邮件,3转存,4归档)',
  `AUTO_NUM_YEAR` int(11) NOT NULL DEFAULT '0' COMMENT '自动编号计数器年刷新',
  `AUTO_NUM_MONTH` int(11) NOT NULL DEFAULT '0' COMMENT '自动编号计数器月刷新',
  `AUTO_NUM_TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `MODULE_NAME` varchar(40) DEFAULT NULL COMMENT '用于区分业务流程平台和其他模块',
  `FLOW_EXTERNAL` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否允许外部调用',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `EXT_USER` varchar(20) DEFAULT NULL,
  `FORM_TYPE` tinyint(3) NOT NULL DEFAULT '100' COMMENT '表单类型，0-自动排版，2-使用已有表单',
  `IS_FORM_VERSION` tinyint(3) NOT NULL DEFAULT '0' COMMENT '表单是否开启版本控制',
  `IS_MOBILE_PROCESS` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否允许手机端发起此流程',
  `VIEW_CHECK` int(1) NOT NULL DEFAULT '1' COMMENT '流程流转是否选中传阅人(0-否,1-是)',
  `IS_ENABLE` tinyint(3) NOT NULL DEFAULT '1' COMMENT '流程是否启用(1-启用,0-未启用)',
  `VIEW_CONTENT` varchar(20) NOT NULL DEFAULT '''1,2,3,4''' COMMENT '传阅内容设置',
  `FREE_BACK` int(1) DEFAULT '0' COMMENT '自由流程是否设置退回步骤',
  `CONTRACT_LOCK` int(1) NOT NULL DEFAULT '0' COMMENT '是否开启契约锁,0-不开启;1-开启',
  `ICON_COLOR` text NOT NULL COMMENT '流程自定义图标',
  `FREE_FLOW_SWITCH` text COMMENT '自由流程开关整合，存放JSON字符串',
  `V13_DEFAULT_FOLD` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '办理界面右侧步骤栏默认收起（只对V13有效）',
  PRIMARY KEY (`FLOW_ID`),
  KEY `FLOW_SORT` (`FLOW_SORT`),
  KEY `FORM_ID` (`FORM_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8 COMMENT='BPM流程';

-- 正在导出表  td_oa_bak.bpm_type 的数据：~2 rows (大约)
INSERT INTO `bpm_type` (`FLOW_ID`, `FLOW_NAME`, `FORM_ID`, `FLOW_DOC`, `FLOW_TYPE`, `MANAGE_USER`, `FLOW_NO`, `FLOW_SORT`, `AUTO_NAME`, `AUTO_NUM`, `AUTO_LEN`, `QUERY_USER`, `FLOW_DESC`, `AUTO_EDIT`, `NEW_USER`, `QUERY_ITEM`, `COMMENT_PRIV`, `DEPT_ID`, `FREE_PRESET`, `FREE_OTHER`, `QUERY_USER_DEPT`, `MANAGE_USER_DEPT`, `EDIT_PRIV`, `LIST_FLDS_STR`, `ALLOW_PRE_SET`, `FORCE_PRE_SET`, `MODEL_ID`, `MODEL_NAME`, `ATTACHMENT_ID`, `ATTACHMENT_NAME`, `VIEW_USER`, `VIEW_DEPT`, `VIEW_ROLE`, `VIEW_PRIV`, `IS_VERSION`, `FLOW_ACTION`, `AUTO_NUM_YEAR`, `AUTO_NUM_MONTH`, `AUTO_NUM_TIME`, `MODULE_NAME`, `FLOW_EXTERNAL`, `CORP_ID`, `EXT_USER`, `FORM_TYPE`, `IS_FORM_VERSION`, `IS_MOBILE_PROCESS`, `VIEW_CHECK`, `IS_ENABLE`, `VIEW_CONTENT`, `FREE_BACK`, `CONTRACT_LOCK`, `ICON_COLOR`, `FREE_FLOW_SWITCH`, `V13_DEFAULT_FOLD`) VALUES
	(6, '请假申请', 6, 1, 1, 'admin,chr,gb,ym,wj,||', 1, 5, '', 0, 0, '||', '', '1', '', '', '3', 0, 0, 2, '', '', '', '', 0, 0, '', '', '', '', '', '', '', 0, 0, '1,2,3,4,', 0, 0, '1000-01-01 00:00:00', 'approve_center', 0, '', '', 2, 0, 1, 1, 1, '1,2,3,4,', 0, 0, '', NULL, 0),
	(7, '加班申请', 7, 1, 1, '', 0, 5, '', 0, 0, '', '', '1', '', '', '3', 0, 1, 2, '', '', '', '', 0, 0, '', '', '', '', '', '', '', 0, 0, '1,2,3,4,', 0, 0, '1000-01-01 00:00:00', 'approve_center', 0, '', '', 2, 0, 1, 1, 1, '1,2,3,4,', 0, 0, '', NULL, 0);

-- 导出  表 td_oa_bak.bpm_variable 结构
DROP TABLE IF EXISTS `bpm_variable`;
CREATE TABLE IF NOT EXISTS `bpm_variable` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `UID` varchar(40) DEFAULT NULL COMMENT '唯一ID',
  `NAME` varchar(50) DEFAULT NULL COMMENT '变量名称',
  `TYPE` varchar(30) DEFAULT NULL COMMENT '变量类型',
  `DESC` text COMMENT '变量描述',
  `STATUS` varchar(1) NOT NULL DEFAULT '' COMMENT '变量状态',
  `SORT_ID` int(11) DEFAULT NULL COMMENT '类型ID',
  `CODES` mediumtext,
  `ORDER_NO` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序号',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `PROGRESS_NO` int(11) DEFAULT NULL COMMENT '进度条跨度',
  `DATECASUAL` varchar(100) DEFAULT NULL COMMENT '日历控件-自定义格式',
  `LINEWIDTH` varchar(100) DEFAULT NULL COMMENT '控件宽度',
  `LINEHEIGHT` varchar(100) DEFAULT NULL COMMENT '控件高度',
  `IS_CANCEl` tinyint(3) NOT NULL DEFAULT '0' COMMENT '字段是否被删除（0-否，1-是）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1626 DEFAULT CHARSET=utf8 COMMENT='BPM变量';

-- 正在导出表  td_oa_bak.bpm_variable 的数据：~1 rows (大约)
INSERT INTO `bpm_variable` (`ID`, `UID`, `NAME`, `TYPE`, `DESC`, `STATUS`, `SORT_ID`, `CODES`, `ORDER_NO`, `CORP_ID`, `PROGRESS_NO`, `DATECASUAL`, `LINEWIDTH`, `LINEHEIGHT`, `IS_CANCEl`) VALUES
	(66, 'E46845026C3B8C6127FA9B04F64904D6', 'data_m66', 'auto', '申请人所在部门', '', 7, 'SYS_DEPTNAME_SHORT', 0, '', NULL, NULL, '', '', 0);

-- 导出  表 td_oa_bak.bpm_variable_ext 结构
DROP TABLE IF EXISTS `bpm_variable_ext`;
CREATE TABLE IF NOT EXISTS `bpm_variable_ext` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `desc` varchar(200) NOT NULL COMMENT '变量描述',
  `type` varchar(10) NOT NULL COMMENT '变量类型',
  `variable_id` int(10) NOT NULL COMMENT '变量ID',
  `parent_flow_id` int(10) NOT NULL COMMENT '父流程FLOW_ID',
  `server` varchar(200) NOT NULL COMMENT '服务ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='外部变量信息';

-- 正在导出表  td_oa_bak.bpm_variable_ext 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_variable_item_map 结构
DROP TABLE IF EXISTS `bpm_variable_item_map`;
CREATE TABLE IF NOT EXISTS `bpm_variable_item_map` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FORM_ID` int(11) NOT NULL DEFAULT '0' COMMENT '表单',
  `MAP` mediumtext COMMENT '变量和控件对应关系',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='变量和表单控件映射表';

-- 正在导出表  td_oa_bak.bpm_variable_item_map 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_variable_item_map_list 结构
DROP TABLE IF EXISTS `bpm_variable_item_map_list`;
CREATE TABLE IF NOT EXISTS `bpm_variable_item_map_list` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `FORM_ID` int(11) NOT NULL DEFAULT '0' COMMENT '表单',
  `LIST_MAP` mediumtext COMMENT '列表变量和控件对应关系',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='变量和表单列表控件映射表';

-- 正在导出表  td_oa_bak.bpm_variable_item_map_list 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_variable_sort 结构
DROP TABLE IF EXISTS `bpm_variable_sort`;
CREATE TABLE IF NOT EXISTS `bpm_variable_sort` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `FLOW_ID` int(11) DEFAULT NULL COMMENT '流程ID',
  `NAME` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `DESC` text COMMENT '分类描述',
  `TYPE` varchar(10) DEFAULT NULL COMMENT '分类类型（main-单记录，detail-多记录）',
  `STATUS` varchar(1) NOT NULL DEFAULT '' COMMENT '状态',
  `ORDER_NO` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序号',
  `UID` varchar(40) DEFAULT NULL COMMENT '列表控件变量标识',
  `SYSTEM` tinyint(3) DEFAULT '0',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  `IS_CANCEL` tinyint(3) NOT NULL DEFAULT '0' COMMENT '分组是否被删除（0-否，1-是）',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 COMMENT='BPM变量分类';

-- 正在导出表  td_oa_bak.bpm_variable_sort 的数据：~1 rows (大约)
INSERT INTO `bpm_variable_sort` (`ID`, `FLOW_ID`, `NAME`, `DESC`, `TYPE`, `STATUS`, `ORDER_NO`, `UID`, `SYSTEM`, `CORP_ID`, `IS_CANCEL`) VALUES
	(7, 6, 'system_defined_6_group', '请假申请', 'main', '', 0, NULL, 1, '', 0);

-- 导出  表 td_oa_bak.bpm_variable_sort_ext 结构
DROP TABLE IF EXISTS `bpm_variable_sort_ext`;
CREATE TABLE IF NOT EXISTS `bpm_variable_sort_ext` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `flow_id` int(11) NOT NULL,
  `flow_name` varchar(200) NOT NULL COMMENT '流程名称',
  `category` varchar(200) NOT NULL COMMENT '流程分类',
  `type` int(10) DEFAULT NULL,
  `level` int(10) DEFAULT NULL,
  `node` varchar(200) DEFAULT NULL,
  `server` varchar(200) NOT NULL COMMENT '服务ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='外部流程信息';

-- 正在导出表  td_oa_bak.bpm_variable_sort_ext 的数据：~0 rows (大约)

-- 导出  表 td_oa_bak.bpm_version 结构
DROP TABLE IF EXISTS `bpm_version`;
CREATE TABLE IF NOT EXISTS `bpm_version` (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `RUN_ID` int(11) NOT NULL COMMENT '流程实例ID',
  `PRCS_ID` int(11) NOT NULL COMMENT '流程实例步骤ID',
  `FLOW_PRCS` int(11) NOT NULL COMMENT '流程步骤ID',
  `ITEM_ID` int(11) NOT NULL COMMENT '表单字段ID',
  `ITEM_DATA` mediumtext NOT NULL COMMENT '表单字段的数据',
  `TIME` datetime NOT NULL DEFAULT '1000-01-01 00:00:00',
  `MARK` int(11) NOT NULL COMMENT '版本号',
  `CORP_ID` char(36) NOT NULL DEFAULT '' COMMENT '企业id',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='BPM流程版本控制(历史数据)';

-- 正在导出表  td_oa_bak.bpm_version 的数据：~0 rows (大约)

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
