-- 用户登录后台系统测试数据初始化脚本
-- 适用于达梦数据库

-- 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS SYSDBA.TD_USER (
    UID NUMBER(10) PRIMARY KEY,
    USER_ID VARCHAR2(50) UNIQUE NOT NULL,
    USER_NAME VARCHAR2(100) NOT NULL,
    PASSWORD VARCHAR2(100) NOT NULL,
    BYNAME VARCHAR2(50),
    DEPT_ID NUMBER(10),
    USER_PRIV NUMBER(2) DEFAULT 1,
    USER_PRIV_OTHER NUMBER(2) DEFAULT 1,
    SYS_ADMIN NUMBER(1) DEFAULT 0,
    MOBILE VARCHAR2(20),
    EMAIL VARCHAR2(100),
    POSITION VARCHAR2(100),
    USER_SEX NUMBER(1) DEFAULT 0,
    AVATAR NUMBER(10) DEFAULT 0,
    THEME NUMBER(10) DEFAULT 24,
    THEME_COLOUR VARCHAR2(20) DEFAULT 'darkblue',
    MOBILE_FONTSIZE VARCHAR2(20) DEFAULT 'medium',
    CALL_TYPE NUMBER(1) DEFAULT 0,
    WATERMARK_JSON CLOB,
    IS_GROUP NUMBER(1) DEFAULT 0,
    USER_PRIV_TYPE NUMBER(1) DEFAULT 1,
    USER_MANAGE_ORGS VARCHAR2(500),
    WEATHER_CITY VARCHAR2(100) DEFAULT '北京_北京_北京',
    NOT_VIEW_USER NUMBER(1) DEFAULT 0,
    ANOTHER NUMBER(1) DEFAULT 0,
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13),
    CREATE_USER NUMBER(10),
    UPDATE_USER NUMBER(10)
);

-- 创建部门表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_DEPT (
    DEPT_ID NUMBER(10) PRIMARY KEY,
    DEPT_NAME VARCHAR2(100) NOT NULL,
    PARENT_ID NUMBER(10) DEFAULT 0,
    DEPT_SORT NUMBER(10) DEFAULT 0,
    DEPT_DESC VARCHAR2(500),
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13)
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_ROLE (
    ROLE_ID NUMBER(10) PRIMARY KEY,
    ROLE_NAME VARCHAR2(100) NOT NULL,
    ROLE_DESC VARCHAR2(500),
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13)
);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_USER_ROLE (
    ID NUMBER(10) PRIMARY KEY,
    USER_ID NUMBER(10) NOT NULL,
    ROLE_ID NUMBER(10) NOT NULL,
    CREATE_TIME NUMBER(13)
);

-- 创建邮件表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_EMAIL (
    ID NUMBER(10) PRIMARY KEY,
    SENDER_ID NUMBER(10) NOT NULL,
    RECEIVER_ID NUMBER(10) NOT NULL,
    SUBJECT VARCHAR2(200) NOT NULL,
    CONTENT CLOB,
    EMAIL_TYPE VARCHAR2(20) DEFAULT 'normal',
    PRIORITY VARCHAR2(20) DEFAULT 'normal',
    PARENT_ID NUMBER(10) DEFAULT 0,
    IS_READ NUMBER(1) DEFAULT 0,
    READ_TIME NUMBER(13),
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13)
);

-- 创建会议室表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_MEETING_ROOM (
    ROOM_ID NUMBER(10) PRIMARY KEY,
    ROOM_NAME VARCHAR2(100) NOT NULL,
    CAPACITY NUMBER(10),
    LOCATION VARCHAR2(200),
    EQUIPMENT VARCHAR2(500),
    STATUS NUMBER(1) DEFAULT 1,
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13)
);

-- 创建会议表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_MEETING (
    MEETING_ID NUMBER(10) PRIMARY KEY,
    ROOM_ID NUMBER(10) NOT NULL,
    TITLE VARCHAR2(200) NOT NULL,
    DESCRIPTION CLOB,
    START_TIME NUMBER(13) NOT NULL,
    END_TIME NUMBER(13) NOT NULL,
    ORGANIZER_ID NUMBER(10) NOT NULL,
    PARTICIPANTS VARCHAR2(1000),
    STATUS VARCHAR2(20) DEFAULT 'confirmed',
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13),
    CREATE_USER NUMBER(10),
    UPDATE_USER NUMBER(10),
    CANCEL_TIME NUMBER(13),
    CANCEL_USER NUMBER(10)
);

-- 创建文档表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_DOCUMENT (
    DOC_ID NUMBER(10) PRIMARY KEY,
    TITLE VARCHAR2(200) NOT NULL,
    DOC_NUMBER VARCHAR2(100),
    DOC_TYPE VARCHAR2(50),
    SENDER VARCHAR2(100),
    CONTENT CLOB,
    PRIORITY VARCHAR2(20) DEFAULT 'normal',
    STATUS VARCHAR2(20) DEFAULT 'draft',
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13),
    CREATE_USER NUMBER(10),
    UPDATE_USER NUMBER(10),
    DELETE_TIME NUMBER(13),
    DELETE_USER NUMBER(10)
);

-- 创建附件表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_ATTACHMENT (
    ATTACHMENT_ID NUMBER(10) PRIMARY KEY,
    RELATION_ID NUMBER(10) NOT NULL,
    RELATION_TYPE VARCHAR2(50) NOT NULL,
    FILE_NAME VARCHAR2(200) NOT NULL,
    FILE_PATH VARCHAR2(500) NOT NULL,
    FILE_SIZE NUMBER(15),
    FILE_TYPE VARCHAR2(20),
    CREATE_TIME NUMBER(13),
    CREATE_USER NUMBER(10)
);

-- 创建日程表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_SCHEDULE (
    SCHEDULE_ID NUMBER(10) PRIMARY KEY,
    USER_ID NUMBER(10) NOT NULL,
    TITLE VARCHAR2(200) NOT NULL,
    DESCRIPTION CLOB,
    START_TIME NUMBER(13) NOT NULL,
    END_TIME NUMBER(13) NOT NULL,
    SCHEDULE_TYPE VARCHAR2(50) DEFAULT 'personal',
    LOCATION VARCHAR2(200),
    REMINDER_TIME NUMBER(13) DEFAULT 0,
    STATUS VARCHAR2(20) DEFAULT 'active',
    CREATE_TIME NUMBER(13),
    UPDATE_TIME NUMBER(13),
    CREATE_USER NUMBER(10),
    UPDATE_USER NUMBER(10)
);

-- 创建日志表
CREATE TABLE IF NOT EXISTS SYSDBA.TD_LOG (
    LOG_ID NUMBER(10) PRIMARY KEY,
    USER_ID NUMBER(10),
    ACTION VARCHAR2(100),
    CONTENT VARCHAR2(500),
    DATA CLOB,
    IP VARCHAR2(50),
    USER_AGENT VARCHAR2(500),
    CREATE_TIME NUMBER(13)
);

-- 插入测试数据

-- 插入部门数据
INSERT INTO SYSDBA.TD_DEPT (DEPT_ID, DEPT_NAME, PARENT_ID, DEPT_SORT, CREATE_TIME) VALUES 
(1, '四川省住房和城乡建设厅', 0, 1, 1640995200000);
INSERT INTO SYSDBA.TD_DEPT (DEPT_ID, DEPT_NAME, PARENT_ID, DEPT_SORT, CREATE_TIME) VALUES 
(2, '办公室', 1, 1, 1640995200000);
INSERT INTO SYSDBA.TD_DEPT (DEPT_ID, DEPT_NAME, PARENT_ID, DEPT_SORT, CREATE_TIME) VALUES 
(3, '质量安全监管处', 1, 2, 1640995200000);
INSERT INTO SYSDBA.TD_DEPT (DEPT_ID, DEPT_NAME, PARENT_ID, DEPT_SORT, CREATE_TIME) VALUES 
(4, '建筑市场监管处', 1, 3, 1640995200000);

-- 插入角色数据
INSERT INTO SYSDBA.TD_ROLE (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATE_TIME) VALUES 
(1, '系统管理员', '系统管理员角色', 1640995200000);
INSERT INTO SYSDBA.TD_ROLE (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATE_TIME) VALUES 
(2, '部门领导', '部门领导角色', 1640995200000);
INSERT INTO SYSDBA.TD_ROLE (ROLE_ID, ROLE_NAME, ROLE_DESC, CREATE_TIME) VALUES 
(3, '普通用户', '普通用户角色', 1640995200000);

-- 插入测试用户（密码为MD5加密的admin）
INSERT INTO SYSDBA.TD_USER (UID, USER_ID, USER_NAME, PASSWORD, BYNAME, DEPT_ID, USER_PRIV, SYS_ADMIN, MOBILE, EMAIL, POSITION, CREATE_TIME) VALUES 
(1, 'admin', '系统管理员', '21232f297a57a5a743894a0e4a801fc3', 'admin', 2, 1, 1, '13800138001', '<EMAIL>', '系统管理员', 1640995200000);

INSERT INTO SYSDBA.TD_USER (UID, USER_ID, USER_NAME, PASSWORD, BYNAME, DEPT_ID, USER_PRIV, SYS_ADMIN, MOBILE, EMAIL, POSITION, CREATE_TIME) VALUES 
(2, 'zhangsan', '张三', '21232f297a57a5a743894a0e4a801fc3', '张三', 2, 1, 0, '13800138002', '<EMAIL>', '科员', 1640995200000);

INSERT INTO SYSDBA.TD_USER (UID, USER_ID, USER_NAME, PASSWORD, BYNAME, DEPT_ID, USER_PRIV, SYS_ADMIN, MOBILE, EMAIL, POSITION, CREATE_TIME) VALUES 
(3, 'lisi', '李四', '21232f297a57a5a743894a0e4a801fc3', '李四', 3, 1, 0, '13800138003', '<EMAIL>', '科长', 1640995200000);

-- 插入用户角色关联
INSERT INTO SYSDBA.TD_USER_ROLE (ID, USER_ID, ROLE_ID, CREATE_TIME) VALUES 
(1, 1, 1, 1640995200000);
INSERT INTO SYSDBA.TD_USER_ROLE (ID, USER_ID, ROLE_ID, CREATE_TIME) VALUES 
(2, 2, 3, 1640995200000);
INSERT INTO SYSDBA.TD_USER_ROLE (ID, USER_ID, ROLE_ID, CREATE_TIME) VALUES 
(3, 3, 2, 1640995200000);

-- 插入会议室数据
INSERT INTO SYSDBA.TD_MEETING_ROOM (ROOM_ID, ROOM_NAME, CAPACITY, LOCATION, EQUIPMENT, CREATE_TIME) VALUES 
(1, '第一会议室', 20, '办公楼3楼', '投影仪,音响,白板', 1640995200000);
INSERT INTO SYSDBA.TD_MEETING_ROOM (ROOM_ID, ROOM_NAME, CAPACITY, LOCATION, EQUIPMENT, CREATE_TIME) VALUES 
(2, '第二会议室', 50, '办公楼4楼', '投影仪,音响,白板,视频会议设备', 1640995200000);
INSERT INTO SYSDBA.TD_MEETING_ROOM (ROOM_ID, ROOM_NAME, CAPACITY, LOCATION, EQUIPMENT, CREATE_TIME) VALUES 
(3, '小型会议室', 8, '办公楼2楼', '白板', 1640995200000);

-- 插入测试邮件数据
INSERT INTO SYSDBA.TD_EMAIL (ID, SENDER_ID, RECEIVER_ID, SUBJECT, CONTENT, EMAIL_TYPE, CREATE_TIME) VALUES 
(1, 1, 2, '系统测试邮件', '这是一封系统测试邮件，请查收。', 'normal', 1640995200000);
INSERT INTO SYSDBA.TD_EMAIL (ID, SENDER_ID, RECEIVER_ID, SUBJECT, CONTENT, EMAIL_TYPE, CREATE_TIME) VALUES 
(2, 2, 1, '工作汇报', '本周工作汇报内容...', 'normal', 1640995200000);

-- 插入测试文档数据
INSERT INTO SYSDBA.TD_DOCUMENT (DOC_ID, TITLE, DOC_NUMBER, DOC_TYPE, SENDER, CONTENT, STATUS, CREATE_TIME, CREATE_USER) VALUES 
(1, '关于加强建筑工程质量安全管理的通知', '川建办发〔2024〕15号', '通知', '四川省住房和城乡建设厅', '为进一步加强建筑工程质量安全管理...', 'published', 1640995200000, 1);

-- 插入测试日程数据
INSERT INTO SYSDBA.TD_SCHEDULE (SCHEDULE_ID, USER_ID, TITLE, DESCRIPTION, START_TIME, END_TIME, SCHEDULE_TYPE, CREATE_TIME, CREATE_USER) VALUES 
(1, 1, '部门例会', '每周部门例会', 1641081600000, 1641085200000, 'meeting', 1640995200000, 1);

COMMIT;
