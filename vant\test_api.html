<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>API测试</h1>
    <button onclick="testAPI()">测试 getFormData API</button>
    <div id="result"></div>

    <script>
    async function testAPI() {
        const token = '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';
        
        const url = 'http://127.0.0.1/general/document_manage/index.php/api/Form/getFormData?PRCS_KEY_ID=1071170&run_id=164635&flow_id=15&prcs_id=5&flow_prcs=30';
        
        try {
            console.log('=== 开始API测试 ===');
            console.log('请求URL:', url);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('响应状态:', response.status);
            console.log('响应头:', response.headers);
            
            const text = await response.text();
            console.log('原始响应:', text);
            
            let data;
            try {
                data = JSON.parse(text);
                console.log('解析后的数据:', data);
            } catch (parseError) {
                console.error('JSON解析失败:', parseError);
                data = { error: 'JSON解析失败', raw: text };
            }
            
            document.getElementById('result').innerHTML = `
                <h2>API测试结果</h2>
                <p><strong>状态:</strong> ${response.status}</p>
                <p><strong>响应:</strong></p>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            
        } catch (error) {
            console.error('请求失败:', error);
            document.getElementById('result').innerHTML = `
                <h2>API测试失败</h2>
                <p><strong>错误:</strong> ${error.message}</p>
            `;
        }
    }
    </script>
</body>
</html>
