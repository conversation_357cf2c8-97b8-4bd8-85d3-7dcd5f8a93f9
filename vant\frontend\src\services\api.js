import { config } from '../utils/config.js'

// 通用的 fetch 封装
async function fetchData(url, options = {}) {
  console.log('=== API请求参数 ===');
  console.log('请求URL:', url);
  // 如果配置为不使用API，直接使用本地数据
  if (!config.USE_API) {
    return await fetchLocalData(url)
  }

  try {
    // 获取 token
    const token = localStorage.getItem('auth_token');

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // 添加 Authorization header（如果有 token）
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const fullUrl = `${config.API_BASE_URL}${url}`;

    // 详细日志：请求信息
    console.log('=== API请求开始 ===');
    console.log('请求URL:', fullUrl);
    console.log('请求方法:', options.method || 'GET');
    console.log('请求头:', headers);
    console.log('请求体:', options.body || '无');
    console.log('==================');
    console.log(fullUrl,'========123==========');
    const response = await fetch(fullUrl, {
      timeout: config.REQUEST_TIMEOUT,
      headers,
      ...options
    })

    // 详细日志：响应状态
    console.log('=== API响应状态 ===');
    console.log('响应状态码:', response.status);
    console.log('响应状态文本:', response.statusText);
    console.log('==================');

    if (!response.ok) {
      console.error('=== HTTP错误 ===');
      console.error('状态码:', response.status);
      console.error('状态文本:', response.statusText);
      console.error('===============');

      // 处理 401 错误
      if (response.status === 401) {
        localStorage.removeItem('auth_token');
        console.warn('Token 已过期，请重新登录');
        throw new Error('认证失败，请重新登录');
      }
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const responseText = await response.text();
    console.log('=== 原始响应内容 ===');
    console.log('响应文本:', responseText);
    console.log('==================');

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (parseError) {
      console.error('=== JSON解析错误 ===');
      console.error('解析错误:', parseError);
      console.error('原始响应:', responseText);
      console.error('==================');
      throw new Error('响应数据格式错误，无法解析JSON');
    }

    console.log('=== 解析后的响应数据 ===');
    console.log('完整响应对象:', responseData);
    console.log('========================');

    const { data, code, msg } = responseData;

    console.log('=== 响应数据结构 ===');
    console.log('code:', code);
    console.log('msg:', msg);
    console.log('data:', data);
    console.log('==================');

    if(code !== 200){
        console.error('=== API业务错误 ===');
        console.error('错误代码:', code);
        console.error('错误消息:', msg);
        console.error('==================');
        throw new Error(`API status error! code:${code},msg: ${msg}`)
    }

    console.log('=== API请求成功 ===');
    console.log('返回数据:', data);
    console.log('==================');

    return data;
  } catch (error) {
    console.error('=== API请求失败 ===');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    console.error('请求URL:', `${config.API_BASE_URL}${url}`);
    console.error('==================');

    // 不再回退到本地数据，直接抛出错误
    throw error;
  }
}

// 回退到本地JSON文件的方法
async function fetchLocalData(url) {
  // 提取基础路径（去掉查询参数）
  const baseUrl = url.split('?')[0]
  // 解析查询参数（统一处理）
  const urlParams = new URLSearchParams(url.split('?')[1] || '')

  // 直接导入本地JSON文件
  try {
    switch (baseUrl) {
      case '/Auth/tabs':
        return (await import('../data/tabs.json')).default
      case '/Contact/contacts':
        // 对于联系人数据，可以在这里处理部门筛选逻辑
        const contactsData = (await import('../data/contacts.json')).default

        const deptId = urlParams.get('dept_id')

        if (deptId) {
          // 如果有部门ID，可以在这里进行筛选
          console.log('演示模式：按部门筛选联系人，部门ID:', deptId)
          // 这里可以添加筛选逻辑，暂时返回所有数据
        }

        return contactsData
      case '/Contact/contactsGroups':
        return (await import('../data/contactsGroups.json')).default
      case '/Document/list':
        // 处理文档列表的分页参数
        const documentsData = (await import('../data/documents.json')).default

        const page = parseInt(urlParams.get('page')) || 1
        const pageSize = parseInt(urlParams.get('page_size')) || 10
        const flowId = urlParams.get('flow_id')

        // 如果有流程ID筛选，可以在这里进行筛选
        let filteredList = documentsData.list
        if (flowId) {
          const flowIds = flowId.split(',').map(id => parseInt(id))
          filteredList = documentsData.list.filter(item => flowIds.includes(item.FLOW_ID))
        }

        // 计算分页
        const startIndex = (page - 1) * pageSize
        const endIndex = startIndex + pageSize
        const paginatedList = filteredList.slice(startIndex, endIndex)

        return {
          list: paginatedList,
          page: page,
          page_size: pageSize,
          total: filteredList.length,
          total_pages: Math.ceil(filteredList.length / pageSize)
        }
      case '/Auth/officeApps':
        return (await import('../data/officeApps.json')).default
      case '/Auth/quickActions':
        return (await import('../data/quickActions.json')).default
      case '/api/emails':
        return (await import('../data/emails.json')).default
      case '/api/organization':
        return (await import('../data/organization.json')).default
      case '/api/users':
        return (await import('../data/users.json')).default
      case '/document-detail':
        // 处理表单数据请求
        const runId = urlParams.get('run_id')
        const formFlowId = urlParams.get('flow_id')
        const prcsKeyId = urlParams.get('PRCS_KEY_ID')
        const prcsId = urlParams.get('prcs_id')
        const flowPrcs = urlParams.get('flow_prcs')

        console.log('演示模式：获取表单数据', { runId, formFlowId, prcsKeyId, prcsId, flowPrcs })

        // 返回模拟的表单数据
        return {
          config: [
            {
              item_id: 'title',
              tag: 'input',
              name: 'title',
              title: '文档标题',
              type: 'text',
              value: '关于进一步加强建筑工程质量安全管理的通知'
            },
            {
              item_id: 'content',
              tag: 'textarea',
              name: 'content',
              title: '文档内容',
              type: 'textarea',
              value: '为进一步加强我市建筑工程质量安全管理，确保人民群众生命财产安全，现就有关事项通知如下...'
            },
            {
              item_id: 'sender',
              tag: 'input',
              name: 'sender',
              title: '发送单位',
              type: 'text',
              value: '雅安市城乡规划建设和住房保障局'
            },
            {
              item_id: 'date',
              tag: 'input',
              name: 'date',
              title: '发文日期',
              type: 'date',
              value: '2024-03-15'
            }
          ],
          data: {
            main: {
              title: '关于进一步加强建筑工程质量安全管理的通知',
              content: '为进一步加强我市建筑工程质量安全管理，确保人民群众生命财产安全，现就有关事项通知如下：\n\n一、严格执行工程质量管理制度\n二、加强施工现场安全监管\n三、完善质量安全责任体系',
              sender: '雅安市城乡规划建设和住房保障局',
              date: '2024-03-15',
              run_id: runId,
              flow_id: formFlowId
            }
          },
          canEdit: prcsId ? true : false,
          runId: runId,
          flowId: formFlowId
        }
      default:
        throw new Error(`未找到对应的本地数据文件: ${url}`)
    }
  } catch (error) {
    console.error('加载本地数据失败:', error)
    throw error
  }
}

// API 接口方法
export const api = {
  // 用户登录
  async login(username, password) {
    if (!config.USE_API) {
      // 模拟登录逻辑
      if (username && password) {
        return {
          code: 200,
          msg: '登录成功',
          data: {
            user: {
              id: 1,
              username: username,
              name: '测试用户',
              dept_name: '测试部门'
            },
            token: 'mock_token_' + Date.now()
          },
          type: 'success'
        }
      } else {
        return {
          code: 400,
          msg: '用户名和密码不能为空',
          type: 'error'
        }
      }
    }

    try {
      const response = await fetch(`${config.API_BASE_URL}/Auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: username,
          password: password
        }),
        timeout: config.REQUEST_TIMEOUT
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      // 保存 token
      if (result.code === 200 && result.data && result.data.token) {
        localStorage.setItem('auth_token', result.data.token);
      }

      return result
    } catch (error) {
      console.error('登录请求失败:', error)
      throw error
    }
  },

  // 用户登出
  async logout() {
    try {
      await fetchData('/Auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      // 无论成功失败都清除本地 token
      localStorage.removeItem('auth_token');
    }
  },

  // 获取用户信息
  async getUserInfo() {
    if (!config.USE_API) {
      return {
        code: 200,
        msg: '获取成功',
        data: {
          id: 1,
          username: 'testuser',
          name: '测试用户',
          dept_name: '测试部门'
        },
        type: 'success'
      }
    }

    try {
      const response = await fetch(`${config.API_BASE_URL}/Auth/userInfo`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        credentials: 'include', // 包含cookies
        timeout: config.REQUEST_TIMEOUT
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },

  // 用户登出
  async logout() {
    if (!config.USE_API) {
      return {
        code: 200,
        msg: '登出成功',
        type: 'success'
      }
    }

    try {
      const response = await fetch(`${config.API_BASE_URL}/Auth/logout`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
        },
        credentials: 'include', // 包含cookies
        timeout: config.REQUEST_TIMEOUT
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('登出请求失败:', error)
      throw error
    }
  },

  // 获取底部导航标签
  async getTabs() {
    return await fetchData('/Auth/tabs')
  },

  // 获取联系人详情列表
  async getContacts(params = {}) {
    // 构建查询参数
    const queryParams = new URLSearchParams()
    if (params.dept_id) {
      queryParams.append('dept_id', params.dept_id)
    }
    if (params.name) {
      queryParams.append('name', params.name)
    }
    if (params.start !== undefined) {
      queryParams.append('start', params.start)
    }
    if (params.limit !== undefined) {
      queryParams.append('limit', params.limit)
    }

    const queryString = queryParams.toString()
    const url = queryString ? `/Contact/contactsList?${queryString}` : '/Contact/contactsList'

    return await fetchData(url)
  },

  // 获取联系人分组
  async getContactsGroups() {
    return await fetchData('/Contact/contactsGroups')
  },

  // 获取文档列表
  async getDocuments(params = {}) {
    // 构建查询参数
    const queryParams = new URLSearchParams()
    if (params.page) {
      queryParams.append('page', params.page)
    }
    if (params.page_size) {
      queryParams.append('page_size', params.page_size)
    }
    if (params.flow_id) {
      queryParams.append('flow_id', Array.isArray(params.flow_id) ? params.flow_id.join(',') : params.flow_id)
    }
    if (params.is_include !== undefined) {
      queryParams.append('is_include', params.is_include)
    }

    const queryString = queryParams.toString()
    const url = queryString ? `/Document/list?${queryString}` : '/Document/list'

    return await fetchData(url)
  },

  // 获取办公应用列表
  async getOfficeApps() {
    return await fetchData('/Auth/officeApps')
  },

  // 获取动态表单数据
  async getFormData(params) {
    const queryParams = new URLSearchParams()

    if (params.run_id) {
      queryParams.append('run_id', params.run_id)
    }
    if (params.flow_id) {
      queryParams.append('flow_id', params.flow_id)
    }
    if (params.PRCS_KEY_ID) {
      queryParams.append('PRCS_KEY_ID', params.PRCS_KEY_ID)
    }
    if (params.prcs_id) {
      queryParams.append('prcs_id', params.prcs_id)
    }
    if (params.flow_prcs) {
      queryParams.append('flow_prcs', params.flow_prcs)
    }

    const queryString = queryParams.toString()
    const url = queryString ? `/Form/getFormData?${queryString}` : '/Form/getFormData'

    return await fetchData(url)
  },

  // 获取快捷操作列表
  async getQuickActions() {
    return await fetchData('/Auth/quickActions')
  },

  // 获取邮件列表
  async getEmails() {
    return await fetchData('/api/emails')
  },

  // 获取组织架构数据
  async getOrganization() {
    const orgData = await fetchData('/api/organization')
    const users = await fetchData('/api/users')
    return {
      ...orgData,
      users: users
    }
  }
}

// 默认导出
export default api
