import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Home from '../views/Home.vue'
import Documents from '../views/Documents.vue'
import Detail from '../views/Detail.vue'
import DocumentDetail from '../views/DocumentDetail.vue'
import Transfer from '../views/Transfer.vue'
import Contacts from '../views/Contacts.vue'
import ContactDetail from '../views/ContactDetail.vue'
import Profile from '../views/Profile.vue'
import Email from '../views/Email.vue'
import EmailDetail from '../views/EmailDetail.vue'
import ComposeEmail from '../views/ComposeEmail.vue'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/documents',
    name: 'Documents',
    component: Documents
  },
  {
    path: '/detail',
    name: 'Detail',
    component: Detail
  },
  {
    path: '/document-detail',
    name: 'DocumentDetail',
    component: DocumentDetail
  },
  {
    path: '/transfer',
    name: 'Transfer',
    component: Transfer
  },
  {
    path: '/contacts',
    name: 'Contacts',
    component: Contacts
  },
  {
    path: '/contact-detail',
    name: 'ContactDetail',
    component: ContactDetail
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile
  },
  {
    path: '/email',
    name: 'Email',
    component: Email
  },
  {
    path: '/email-detail',
    name: 'EmailDetail',
    component: EmailDetail
  },
  {
    path: '/compose-email',
    name: 'ComposeEmail',
    component: ComposeEmail
  },
  // 其他页面路由（暂时跳转到首页）
  {
    path: '/sent',
    redirect: '/documents'
  },
  {
    path: '/review',
    redirect: '/documents'
  },
  {
    path: '/inquiry',
    redirect: '/documents'
  },
  {
    path: '/schedule',
    name: 'Schedule',
    component: () => import('../views/Schedule.vue')
  },
  {
    path: '/add-schedule',
    name: 'AddSchedule',
    component: () => import('../views/AddSchedule.vue')
  },

  {
    path: '/notice',
    redirect: '/home'
  },

  {
    path: '/policy',
    redirect: '/home'
  },
  {
    path: '/news',
    redirect: '/home'
  },
  {
    path: '/file-search',
    redirect: '/home'
  },
  {
    path: '/leave',
    redirect: '/home'
  },
  {
    path: '/application',
    redirect: '/home'
  },
  {
    path: '/salary',
    redirect: '/home'
  },
  {
    path: '/notification',
    redirect: '/home'
  },
  {
    path: '/request',
    redirect: '/home'
  },
  {
    path: '/party',
    redirect: '/home'
  },
  {
    path: '/security-settings',
    name: 'SecuritySettings',
    component: () => import('../views/SecuritySettings.vue')
  },
  {
    path: '/change-password',
    name: 'ChangePassword',
    component: () => import('../views/ChangePassword.vue')
  },
  {
    path: '/about-app',
    name: 'AboutApp',
    component: () => import('../views/AboutApp.vue')
  },
  {
    path: '/meeting',
    name: 'Meeting',
    component: () => import('../views/Meeting.vue')
  },
  {
    path: '/meeting-room-booking',
    name: 'MeetingRoomBooking',
    component: () => import('../views/MeetingRoomBooking.vue')
  },
  {
    path: '/meeting-booking-form',
    name: 'MeetingBookingForm',
    component: () => import('../views/MeetingBookingForm.vue')
  },
  {
    path: '/meeting-application',
    name: 'MeetingApplication',
    component: () => import('../views/MeetingApplication.vue')
  },
  {
    path: '/webview',
    name: 'WebView',
    component: () => import('../views/WebView.vue')
  },
  {
    path: '/document/:id',
    name: 'DocumentDetailWithId',
    component: () => import('../views/DocumentDetail.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
