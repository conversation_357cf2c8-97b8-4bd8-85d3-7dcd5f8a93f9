<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\api\lib\BaseController;
use think\facade\Db;

/**
 * 文档控制器
 */
class Document extends BaseController
{
    /**
     * 获取我的流程列表
     * @return \think\Response
     */
    public function list()
    {
        try {
            // 检查用户认证
            $this->checkAuth();

            $params = $this->request->param();

            // 获取参数
            $flowId = $params['flow_id'] ?? [14, 15, 22, 24]; // 默认流程ID
            $page = intval($params['page'] ?? 1);
            $pageSize = intval($params['page_size'] ?? 10);
            $isInclude = $params['is_include'] ?? true; // 是否包含指定流程ID

            // 如果传入的是字符串，转换为数组
            if (is_string($flowId)) {
                $flowId = explode(',', $flowId);
                $flowId = array_map('intval', $flowId);
            }

            $result = $this->getMyFlowList($flowId, $page, $pageSize, $isInclude);

            return $this->success([
                'list' => $result['data'],
                'page' => $result['current_page'],
                'page_size' => $result['per_page'],
                'total' => $result['total'],
                'total_pages' => $result['last_page']
            ], '获取流程列表成功');

        } catch (\Exception $e) {
            return $this->error('获取流程列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取我的流程列表（核心方法）
     * @param array $flowId 流程ID数组
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param bool $isInclude 是否包含指定流程ID
     * @return array
     */
    private function getMyFlowList($flowId = [14, 15, 22, 24], $page = 1, $pageSize = 10, $isInclude = true)
    {
        $userId = $this->getUserId();
        // 构建流程ID条件
        $flowCondition = $isInclude ? 'in' : 'not in';

        // 构建查询
        $query = Db::table($this->getModeTableName('BPM_RUN_PRCS'))
            ->alias('prcs')
            ->leftJoin($this->getModeTableName('BPM_RUN') . ' run', 'prcs.RUN_ID = run.RUN_ID')
            ->leftJoin($this->getModeTableName('BPM_TYPE') . ' type', 'run.FLOW_ID = type.FLOW_ID')
            ->leftJoin($this->getModeTableName('TD_USER') . ' theuser', 'run.BEGIN_USER = theuser.USER_ID')
            ->leftJoin($this->getModeTableName('DEPARTMENT') . ' dept', 'run.BEGIN_DEPT = dept.DEPT_ID')
            ->leftJoin($this->getModeTableName('BPM_PROCESS') . ' process', 'run.FLOW_ID = process.FLOW_ID and prcs.FLOW_PRCS=process.PRCS_ID')
            ->field([
                'prcs.ID as PRCS_KEY_ID',
                'prcs.PRCS_ID',
                'prcs.PRCS_TIME',
                'prcs.CREATE_TIME',
                'run.RUN_ID',
                'run.FLOW_ID',
                'process.PRCS_NAME',
                'prcs.PRCS_FLAG',
                'prcs.FLOW_PRCS',
                'type.FLOW_NAME',
                'run.RUN_NAME',
                'type.FLOW_TYPE',
                'type.LIST_FLDS_STR',
                'type.FORM_ID',
                'theuser.USER_NAME as BEGIN_USER_NAME',
                'dept.DEPT_NAME as BEGIN_DEPT_NAME'
            ])
            ->where('run.FLOW_ID', $flowCondition, $flowId)
            //->where('prcs.USER_ID', $userId)
            ->where('run.DEL_FLAG', '0')
            ->where('prcs.PRCS_FLAG', 'in', [1, 2])
            ->where('prcs.CHILD_RUN', '0')
            ->where(function($query) {
                $query->where(function($subQuery) {
                    $subQuery->where('run.PARENT_RUN', 0)
                             ->where('prcs.PRCS_ID', '<>', 1);
                })->whereOr('run.PARENT_RUN', '<>', 0);
            })
            ->whereNull('run.END_TIME')
            ->order([
                'run.RUN_ID' => 'DESC',
                'prcs.PRCS_TIME' => 'DESC'
            ]);

        // 使用 ThinkPHP6 的 paginate 方法获取分页数据
        $result = $query->paginate([
            'list_rows' => $pageSize,
            'page' => $page
        ]);

        // 处理数据格式
        $rows = $result->items();
        foreach ($rows as $key => $row) {
            // 格式化流程名称
            $row['PRCS_NAME'] = "第" . $row['PRCS_ID'] . "步：" . $row['PRCS_NAME'];

            // 处理时间格式
            if ($row['PRCS_TIME'] == '1000-01-01 00:00:00') {
                $row['PRCS_TIME'] = $row['CREATE_TIME'];
            }

            // 格式化时间为时间戳（毫秒）
            if ($row['PRCS_TIME']) {
                $row['PRCS_TIME_TIMESTAMP'] = strtotime($row['PRCS_TIME']) * 1000;
            }
            if ($row['CREATE_TIME']) {
                $row['CREATE_TIME_TIMESTAMP'] = strtotime($row['CREATE_TIME']) * 1000;
            }

            $rows[$key] = $row;
        }

        // 返回分页结果
        return [
            'data' => $rows,
            'current_page' => $result->getCurrentPage(),
            'per_page' => $result->listRows(),
            'total' => $result->total(),
            'last_page' => $result->lastPage()
        ];
    }

    /**
     * 获取流程详情
     * @param int $runId 流程运行ID
     * @return \think\Response
     */
    public function flowDetail($runId)
    {
        try {
            if (empty($runId)) {
                return $this->error('流程运行ID不能为空');
            }

            // 获取流程基本信息
            $flowInfo = Db::table($this->getModeTableName('BPM_RUN'))
                ->alias('run')
                ->leftJoin($this->getModeTableName('BPM_TYPE') . ' type', 'run.FLOW_ID = type.FLOW_ID')
                ->leftJoin($this->getModeTableName('TD_USER') . ' user', 'run.BEGIN_USER = user.USER_ID')
                ->leftJoin($this->getModeTableName('DEPARTMENT') . ' dept', 'run.BEGIN_DEPT = dept.DEPT_ID')
                ->field([
                    'run.*',
                    'type.FLOW_NAME',
                    'type.FLOW_TYPE',
                    'type.FORM_ID',
                    'user.USER_NAME as BEGIN_USER_NAME',
                    'dept.DEPT_NAME as BEGIN_DEPT_NAME'
                ])
                ->where('run.RUN_ID', $runId)
                ->find();

            if (!$flowInfo) {
                return $this->error('流程不存在');
            }

            // 获取流程步骤信息
            $processSteps = Db::table($this->getModeTableName('BPM_RUN_PRCS'))
                ->alias('prcs')
                ->leftJoin($this->getModeTableName('BPM_PROCESS') . ' process', function($join) {
                    $join->on('prcs.FLOW_ID', '=', 'process.FLOW_ID')
                         ->on('prcs.FLOW_PRCS', '=', 'process.PRCS_ID');
                })
                ->leftJoin($this->getModeTableName('TD_USER') . ' user', 'prcs.USER_ID = user.USER_ID')
                ->field([
                    'prcs.*',
                    'process.PRCS_NAME',
                    'user.USER_NAME'
                ])
                ->where('prcs.RUN_ID', $runId)
                ->order('prcs.PRCS_TIME', 'asc')
                ->select();

            $flowInfo['process_steps'] = $processSteps;

            return $this->success($flowInfo, '获取流程详情成功');

        } catch (\Exception $e) {
            return $this->error('获取流程详情失败：' . $e->getMessage());
        }
    }

    /**
     * 获取文档详情
     * @param int $id 文档ID
     * @return \think\Response
     */
    public function detail($id)
    {
        try {
            $document = Db::table($this->getModeTableName('TD_DOCUMENT'))
                ->alias('d')
                ->leftJoin($this->getModeTableName('TD_USER') . ' u', 'd.CREATE_USER = u.UID')
                ->field('d.*, u.USER_NAME as CREATE_USER_NAME')
                ->where('d.DOC_ID', $id)
                ->find();
                
            if (!$document) {
                return $this->error('文档不存在');
            }
            
            // 获取文档附件
            $attachments = Db::table($this->getModeTableName('TD_ATTACHMENT'))
                ->where('RELATION_ID', $id)
                ->where('RELATION_TYPE', 'document')
                ->select();
                
            $document['attachments'] = $attachments;
            
            return $this->success($document, '获取文档详情成功');
            
        } catch (\Exception $e) {
            return $this->error('获取文档详情失败：' . $e->getMessage());
        }
    }
    
    /**
     * 添加文档
     * @return \think\Response
     */
    public function add()
    {
        try {
            $params = $this->request->param();
            
            // 验证必填参数
            $this->validateRequired($params, ['title', 'doc_type']);
            
            $documentData = [
                'TITLE' => $params['title'],
                'DOC_NUMBER' => $params['doc_number'] ?? '',
                'DOC_TYPE' => $params['doc_type'],
                'SENDER' => $params['sender'] ?? '',
                'CONTENT' => $params['content'] ?? '',
                'PRIORITY' => $params['priority'] ?? 'normal',
                'STATUS' => $params['status'] ?? 'draft',
                'CREATE_TIME' => time() * 1000,
                'CREATE_USER' => $this->getUserId()
            ];
            
            $result = Db::table($this->getModeTableName('TD_DOCUMENT'))->insertGetId($documentData);
            
            if ($result) {
                $this->logAction('add_document', "添加文档：{$params['title']}", $documentData);
                return $this->success(['doc_id' => $result], '添加文档成功');
            } else {
                return $this->error('添加文档失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('添加文档失败：' . $e->getMessage());
        }
    }
    
    /**
     * 更新文档
     * @param int $id 文档ID
     * @return \think\Response
     */
    public function update($id)
    {
        try {
            $params = $this->request->param();
            
            $document = Db::table($this->getModeTableName('TD_DOCUMENT'))
                ->where('DOC_ID', $id)
                ->find();
                
            if (!$document) {
                return $this->error('文档不存在');
            }
            
            $updateData = [];
            $allowedFields = ['TITLE', 'DOC_NUMBER', 'DOC_TYPE', 'SENDER', 'CONTENT', 'PRIORITY', 'STATUS'];
            
            foreach ($allowedFields as $field) {
                $key = strtolower($field);
                if (isset($params[$key])) {
                    $updateData[$field] = $params[$key];
                }
            }
            
            if (empty($updateData)) {
                return $this->error('没有需要更新的数据');
            }
            
            $updateData['UPDATE_TIME'] = time() * 1000;
            $updateData['UPDATE_USER'] = $this->getUserId();
            
            $result = Db::table($this->getModeTableName('TD_DOCUMENT'))
                ->where('DOC_ID', $id)
                ->update($updateData);
                
            if ($result) {
                $this->logAction('update_document', "更新文档：{$document['TITLE']}", $updateData);
                return $this->success([], '更新文档成功');
            } else {
                return $this->error('更新文档失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('更新文档失败：' . $e->getMessage());
        }
    }
    
    /**
     * 删除文档
     * @param int $id 文档ID
     * @return \think\Response
     */
    public function delete($id)
    {
        try {
            $document = Db::table($this->getModeTableName('TD_DOCUMENT'))
                ->where('DOC_ID', $id)
                ->find();
                
            if (!$document) {
                return $this->error('文档不存在');
            }
            
            // 软删除
            $result = Db::table($this->getModeTableName('TD_DOCUMENT'))
                ->where('DOC_ID', $id)
                ->update([
                    'STATUS' => 'deleted',
                    'DELETE_TIME' => time() * 1000,
                    'DELETE_USER' => $this->getUserId()
                ]);
                
            if ($result) {
                $this->logAction('delete_document', "删除文档：{$document['TITLE']}", ['doc_id' => $id]);
                return $this->success([], '删除文档成功');
            } else {
                return $this->error('删除文档失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('删除文档失败：' . $e->getMessage());
        }
    }
    
    /**
     * 上传文档附件
     * @return \think\Response
     */
    public function upload()
    {
        try {
            $file = $this->request->file('file');
            $docId = $this->request->param('doc_id');
            
            if (!$file) {
                return $this->error('请选择要上传的文件');
            }
            
            // 验证文件类型和大小
            $allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
            $maxSize = 50 * 1024 * 1024; // 50MB
            
            if (!in_array($file->extension(), $allowedTypes)) {
                return $this->error('不支持的文件类型');
            }
            
            if ($file->getSize() > $maxSize) {
                return $this->error('文件大小不能超过50MB');
            }
            
            // 生成文件名
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $file->extension();
            $uploadPath = config('dm_db_extra.oa_attach_path') . 'documents/';
            
            // 确保目录存在
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            // 移动文件
            $filePath = $uploadPath . $fileName;
            if ($file->move($uploadPath, $fileName)) {
                // 保存附件信息到数据库
                $attachmentData = [
                    'RELATION_ID' => $docId,
                    'RELATION_TYPE' => 'document',
                    'FILE_NAME' => $file->getOriginalName(),
                    'FILE_PATH' => $filePath,
                    'FILE_SIZE' => $file->getSize(),
                    'FILE_TYPE' => $file->extension(),
                    'CREATE_TIME' => time() * 1000,
                    'CREATE_USER' => $this->getUserId()
                ];
                
                $attachmentId = Db::table($this->getModeTableName('TD_ATTACHMENT'))->insertGetId($attachmentData);
                
                if ($attachmentId) {
                    $this->logAction('upload_document', "上传文档附件：{$file->getOriginalName()}", $attachmentData);
                    return $this->success([
                        'attachment_id' => $attachmentId,
                        'file_name' => $file->getOriginalName(),
                        'file_size' => $file->getSize()
                    ], '文件上传成功');
                } else {
                    return $this->error('保存附件信息失败');
                }
            } else {
                return $this->error('文件上传失败');
            }
            
        } catch (\Exception $e) {
            return $this->error('文件上传失败：' . $e->getMessage());
        }
    }
    
    /**
     * 下载文档附件
     * @param int $id 附件ID
     * @return \think\Response
     */
    public function download($id)
    {
        try {
            $attachment = Db::table($this->getModeTableName('TD_ATTACHMENT'))
                ->where('ATTACHMENT_ID', $id)
                ->find();
                
            if (!$attachment) {
                return $this->error('附件不存在');
            }
            
            $filePath = $attachment['FILE_PATH'];
            if (!file_exists($filePath)) {
                return $this->error('文件不存在');
            }
            
            $this->logAction('download_document', "下载文档附件：{$attachment['FILE_NAME']}", ['attachment_id' => $id]);
            
            // 返回文件下载
            return download($filePath, $attachment['FILE_NAME']);
            
        } catch (\Exception $e) {
            return $this->error('文件下载失败：' . $e->getMessage());
        }
    }
}
