<?php
namespace app\api\controller;

use app\api\lib\BaseController;
use think\facade\Db;
use think\facade\Cache;

class Form extends BaseController
{
    /**
     * 调试Redis数据
     */
    public function debugRedis()
    {
        try {
            // 连接Redis
            $redis = new \Redis();
            $connected = $redis->connect('127.0.0.1', 6399);

            if (!$connected) {
                return $this->error('Redis连接失败');
            }

            // Redis认证
            $authResult = $redis->auth('7PFWLJtad87i5ZfJvhxq6bj09');
            if (!$authResult) {
                $redis->close();
                return $this->error('Redis认证失败');
            }

            $redis->select(5);

            // 获取所有表单配置键
            $allKeys = $redis->keys("bpm/form/ELEMENT:ARRAY:*");

            $result = [
                'total_keys' => count($allKeys),
                'keys' => $allKeys,
                'sample_data' => []
            ];

            // 获取前几个键的示例数据
            foreach (array_slice($allKeys, 0, 3) as $key) {
                $fields = $redis->hKeys($key);
                $result['sample_data'][$key] = [
                    'fields' => $fields,
                    'has_row' => in_array('row', $fields)
                ];
            }

            $redis->close();

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->error('调试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取动态表单配置和数据
     */
    public function getFormData()
    {

        // 验证用户认证

        // 获取参数
        $params = $this->request->param();
        error_log("接收到的所有参数: " . json_encode($params, JSON_UNESCAPED_UNICODE));

        $runId = $params['run_id'] ?? '';
        $flowId = $params['flow_id'] ?? '';
        $prcsKeyId = $params['PRCS_KEY_ID'] ?? '';
        $prcsId = $params['prcs_id'] ?? '';
        $flowPrcs = $params['flow_prcs'] ?? '';

        error_log("解析后的参数:");
        error_log("  run_id: " . $runId);
        error_log("  flow_id: " . $flowId);
        error_log("  PRCS_KEY_ID: " . $prcsKeyId);
        error_log("  prcs_id: " . $prcsId);
        error_log("  flow_prcs: " . $flowPrcs);

        if (empty($runId) || empty($flowId)) {
            error_log("参数验证失败: 缺少必要参数 run_id 或 flow_id");
            return $this->error('缺少必要参数');
        }
        
        try {
            // 1. 获取表单配置
            error_log("开始获取表单配置, flow_id: " . $flowId);
            $formConfig = $this->getFormConfig($flowId);
            error_log("表单配置获取成功, 字段数量: " . count($formConfig));

            // 2. 获取表单数据
            error_log("开始获取表单数据, run_id: " . $runId . ", flow_id: " . $flowId);
            $formData = $this->getFormDataByRunId($runId, $flowId);
            dd($formData);

            error_log("表单数据获取成功: " . json_encode($formData, JSON_UNESCAPED_UNICODE));

            // 3. 检查编辑权限
            $canEdit = false;
            if (!empty($prcsId)) {
                error_log("检查编辑权限, run_id: " . $runId . ", prcs_id: " . $prcsId);
                $canEdit = $this->checkEditPermission($runId, $prcsId);
                error_log("编辑权限检查结果: " . ($canEdit ? '有权限' : '无权限'));
            } else {
                error_log("未提供 prcs_id，跳过编辑权限检查");
            }

            $result = [
                'config' => $formConfig,
                'data' => $formData,
                'canEdit' => $canEdit,
                'runId' => $runId,
                'flowId' => $flowId
            ];

            error_log("=== 最终返回数据 ===");
            error_log(json_encode($result, JSON_UNESCAPED_UNICODE));
            error_log("===================");

            return $this->success($result);

        } catch (\Exception $e) {
            error_log("=== 获取表单数据异常 ===");
            error_log("异常类型: " . get_class($e));
            error_log("异常消息: " . $e->getMessage());
            error_log("异常文件: " . $e->getFile() . ":" . $e->getLine());
            error_log("异常堆栈: " . $e->getTraceAsString());
            error_log("=======================");
            return $this->error('获取表单数据失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 从Redis获取表单配置
     */
    private function getFormConfig($flowId)
    {
        try {

            // 检查Redis扩展是否可用
            if (!extension_loaded('redis')) {
                error_log("Redis扩展未安装");
                throw new \Exception("Redis扩展未安装");
            }

            // 连接Redis database 5
            $redis = new \Redis();
            $connected = $redis->connect('127.0.0.1', 6399);

            if (!$connected) {
                error_log("Redis连接失败");
                throw new \Exception("Redis连接失败");
            }

            // Redis认证
            $authResult = $redis->auth('7PFWLJtad87i5ZfJvhxq6bj09');
            if (!$authResult) {
                error_log("Redis认证失败");
                $redis->close();
                throw new \Exception("Redis认证失败");
            }
            $redis->select(5);

            //根据获取表单的配置ID
            $formId = Db::table('bpm_type')->where('flow_id', $flowId)->value('form_id');            

            $key = "bpm/form/ELEMENT:ARRAY:{$formId}";
            error_log("查询Redis key: " . $key);

            // 先检查Redis中有哪些相关的键
            $allKeys = $redis->keys("bpm/form/ELEMENT:ARRAY:*");
            error_log("Redis中所有表单配置键: " . json_encode($allKeys));

            $serializedData = $redis->hGet($key, '0');

            if (!$serializedData) {
                error_log("Redis中未找到数据，key: " . $key);

                // 检查这个key是否存在
                $keyExists = $redis->exists($key);
                error_log("Key是否存在: " . ($keyExists ? '是' : '否'));

                if ($keyExists) {
                    // 如果key存在，检查有哪些字段
                    $fields = $redis->hKeys($key);
                    error_log("Key存在但字段0不存在，现有字段: " . json_encode($fields));
                }

                $redis->close();
                throw new \Exception("未找到流程 {$flowId} 的表单配置");
            }

            error_log("从Redis获取到数据，长度: " . strlen($serializedData));

            // 反序列化PHP数据
            $configData = unserialize($serializedData);
            if (!$configData) {
                error_log("数据反序列化失败");
                $redis->close();
                throw new \Exception("表单配置数据反序列化失败");
            }

            // 改进数据结构验证逻辑
            // 原来的代码只检查索引3是否存在，现在我们更加灵活地处理
            if (!is_array($configData)) {
                error_log("配置数据不是数组格式");
                $redis->close();
                throw new \Exception("表单配置数据格式错误：数据不是数组格式");
            }

            // 寻找包含表单字段配置的元素
            $formConfigElement = null;
            foreach ($configData as $index => $element) {
                if (is_array($element) && !empty($element)) {
                    // 查找看起来像表单配置的元素（包含ITEM_ID键的数组）
                    $hasFormFields = false;
                    if (is_array($element)) {
                        foreach ($element as $item) {
                            if (is_array($item) && (isset($item['ITEM_ID']) || isset($item['item_id']) || isset($item['itemId']))) {
                                $hasFormFields = true;
                                break;
                            }
                        }
                    }
                    
                    if ($hasFormFields) {
                        $formConfigElement = $element;
                        error_log("找到表单配置元素，索引: " . $index);
                        break;
                    }
                }
            }
            $redis->close();

            $parsedConfig = $this->parseFormConfig($configData);
            error_log("表单配置解析完成，字段数量: " . count($parsedConfig));

            return $parsedConfig;

        } catch (\Exception $e) {
            error_log("获取表单配置异常: " . $e->getMessage());
            throw new \Exception("获取表单配置失败: " . $e->getMessage());
        }
    }
    
    /**
     * 解析表单配置
     */
    private function parseFormConfig($configArray)
    {
        $fields = [];

        error_log("开始解析表单配置，配置项数量: " . count($configArray));

        foreach ($configArray as $index => $item) {
            if (is_array($item)) {
                // 支持多种键名格式（ITEM_ID, item_id, itemId等）
                $itemId = $item['ITEM_ID'] ?? $item['item_id'] ?? $item['itemId'] ?? null;
                
                if ($itemId !== null) {
                    // 清理和转换字符编码
                    $field = [
                        'item_id' => $this->cleanString($itemId),
                        'tag' => $this->cleanString($item['TAG'] ?? $item['tag'] ?? ''),
                        'name' => $this->cleanString($item['NAME'] ?? $item['name'] ?? ''),
                        'class' => $this->cleanString($item['CLASS'] ?? $item['class'] ?? ''),
                        'src' => $this->cleanString($item['SRC'] ?? $item['src'] ?? ''),
                        'title' => $this->cleanString($item['TITLE'] ?? $item['title'] ?? ''),
                        'type' => $this->cleanString($item['TYPE'] ?? $item['type'] ?? ''),
                        'uid' => $this->cleanString($item['UID'] ?? $item['uid'] ?? ''),
                        'value' => $this->cleanString($item['VALUE'] ?? $item['value'] ?? ''),
                        'content' => $this->cleanString($item['CONTENT'] ?? $item['content'] ?? '')
                    ];

                    $fields[] = $field;
                    error_log("解析字段 {$index}: " . json_encode($field, JSON_UNESCAPED_UNICODE));
                } else {
                    error_log("跳过无效配置项 {$index} (缺少ITEM_ID): " . json_encode($item, JSON_UNESCAPED_UNICODE));
                }
            } else {
                error_log("跳过无效配置项 {$index}: " . json_encode($item, JSON_UNESCAPED_UNICODE));
            }
        }

        error_log("表单配置解析完成，有效字段数量: " . count($fields));
        return $fields;
    }

    /**
     * 清理字符串，确保UTF-8编码
     */
    private function cleanString($str)
    {
        if (!is_string($str)) {
            return (string)$str;
        }

        // 移除无效的UTF-8字符
        $cleaned = mb_convert_encoding($str, 'UTF-8', 'UTF-8');

        // 移除控制字符（除了换行符和制表符）
        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);

        return $cleaned;
    }

    /**
     * 清理数据数组，确保所有字符串都是有效的UTF-8
     */
    private function cleanDataArray($data)
    {
        if (is_array($data)) {
            $cleaned = [];
            foreach ($data as $key => $value) {
                $cleanKey = $this->cleanString($key);
                if (is_array($value)) {
                    $cleaned[$cleanKey] = $this->cleanDataArray($value);
                } else {
                    $cleaned[$cleanKey] = $this->cleanString($value);
                }
            }
            return $cleaned;
        } else {
            return $this->cleanString($data);
        }
    }
    
    /**
     * 获取表单数据
     */
    private function getFormDataByRunId($runId, $flowId)
    {
        
        $data = [];

        try {
            error_log("开始获取表单数据，run_id: {$runId}, flow_id: {$flowId}");

            // 主表数据
            $mainTable = "bpm_data_{$flowId}";
            error_log("查询主表: " . $mainTable);

            if ($this->tableExists($mainTable)) {
                $mainData = Db::table($mainTable)->where('run_id', $runId)->find();
                if ($mainData) {
                    $data['main'] = $this->cleanDataArray($mainData);
                    error_log("主表数据获取成功");
                } else {
                    error_log("主表中未找到数据");
                }
            } else {
                error_log("主表不存在: " . $mainTable);
            }

            // 子表数据（长文本）
            $childTable = "bpm_data_{$flowId}_child";
            error_log("检查子表: " . $childTable);

            if ($this->tableExists($childTable)) {
                $childData = Db::table($childTable)->where('run_id', $runId)->select()->toArray();
                if ($childData) {
                    $data['child'] = $this->cleanDataArray($childData);
                    error_log("子表数据获取成功，记录数: " . count($childData));
                }
            } else {
                error_log("子表不存在: " . $childTable);
            }

            // 列表数据
            $listTables = $this->getListTables($flowId);
            error_log("找到列表表数量: " . count($listTables));

            foreach ($listTables as $listTable) {
                error_log("查询列表表: " . $listTable);
                $listData = Db::table($listTable)->where('run_id', $runId)->select()->toArray();
                if ($listData) {
                    $data['lists'][$listTable] = $this->cleanDataArray($listData);
                    error_log("列表表数据获取成功，记录数: " . count($listData));
                }

                // 列表子表数据
                $listChildTable = $listTable . '_child';
                if ($this->tableExists($listChildTable)) {
                    $listChildData = Db::table($listChildTable)->where('run_id', $runId)->select()->toArray();
                    if ($listChildData) {
                        $data['lists'][$listChildTable] = $this->cleanDataArray($listChildData);
                        error_log("列表子表数据获取成功，记录数: " . count($listChildData));
                    }
                }
            }

            error_log("表单数据获取完成: " . json_encode($data, JSON_UNESCAPED_UNICODE));
            return $data;

        } catch (\Exception $e) {
            error_log("获取表单数据异常: " . $e->getMessage());
            throw new \Exception("获取表单数据失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查编辑权限
     */
    private function checkEditPermission($runId, $prcsId)
    {
        try {
            $userId = $this->getUserId();
            
            $permission = Db::table('bpm_run_prcs')
                ->where('run_id', $runId)
                ->where('user_id', $userId)
                ->where('prcs_id', $prcsId)
                ->whereIn('prcs_flag', [1, 2])
                ->find();
                
            return !empty($permission);
            
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查表是否存在
     */
    private function tableExists($tableName)
    {
        try {
            $result = Db::query("SHOW TABLES LIKE '{$tableName}'");
            return !empty($result);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取列表表名
     */
    private function getListTables($flowId)
    {
        $tables = [];
        try {
            $allTables = Db::query("SHOW TABLES LIKE 'bpm_data_{$flowId}_list_%'");
            foreach ($allTables as $table) {
                $tableName = array_values($table)[0];
                if (!strpos($tableName, '_child')) {
                    $tables[] = $tableName;
                }
            }
        } catch (\Exception $e) {
            // 忽略错误
        }
        return $tables;
    }
}
