<?php
declare (strict_types = 1);

namespace app\api\lib;

use think\App;
use think\exception\HttpResponseException;
use think\facade\Db;

/**
 * API基础控制器
 */
class BaseController extends \app\BaseController
{
    protected $user;
    public function __construct(App $app)
    {
        parent::__construct($app);

        // 不再使用session，完全基于token认证
    }

    /**
     * 检查用户认证
     */
    protected function checkAuth()
    {

        // 获取token
        $token = $this->request->header('Authorization');
        if (!$token) {
            $this->error('缺少Authorization头', [], 401);
        }

        // 解析token获取用户信息
        $userInfo = $this->parseToken($token);
        if (!$userInfo) {
            $this->error('Token无效或已过期', [], 401);
        }

        // 检查token是否过期
        if (isset($userInfo['exp']) && $userInfo['exp'] < time()) {
            $this->error('Token已过期', [], 401);
        }

        // 获取用户信息
        $userId = $userInfo['LOGIN_USER_ID'] ?? null;
        if (!$userId) {
            $this->error('Token中缺少用户ID', [], 401);
        }

        $this->user = Db::table($this->getModeTableName("TD_USER"))
            ->where('TD_USER.USER_ID', $userId)
            ->find();

        if (!$this->user) {
            $this->error('用户信息不存在', [], 401);
        }
    }

    /**
     * 成功响应
     * @param array $data 响应数据
     * @param string $msg 响应消息
     * @param int $code 响应码
     * @return void
     */
    protected function success($data = [], $msg = "操作成功", $code = 200)
    {
        return json([
            "code" => $code,
            "msg" => $msg,
            "data" => $data,
            "type" => "success",
            "timestamp" => time()
        ]);
    }

    /**
     * 错误响应
     * @param string $msg 错误消息
     * @param array $data 响应数据
     * @param int $code 响应码
     * @return void
     */
    protected function error($msg = "操作失败", $data = [], $code = 400)
    {
        // 记录错误信息到日志
        error_log("API错误响应: {$msg} (Code: {$code})");

        // 如果消息为空或者只包含冒号，提供默认消息
        if (empty($msg) || $msg === '：' || $msg === ':') {
            $msg = "操作失败，请稍后重试";
        }

        $response = json([
            "code" => $code,
            "msg" => $msg,
            "data" => $data,
            "type" => "error",
            "timestamp" => time()
        ]);

        // 设置CORS头
        $response->header([
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With',
        ]);

         throw new HttpResponseException($response);
    }

    /**
     * 获取带模式的表名
     * @param string $tableName 表名
     * @return string
     */
    protected function getModeTableName($tableName)
    {
        if (strpos($tableName, ".") !== false) {
            return $tableName;
        } else {
            return config("dm_db_extra.mode") . "." . $tableName;
        }
    }

    /**
     * 构建查询条件
     * @param array $where 查询条件
     * @return \Closure
     */
    protected function getWhere($where)
    {
        if (!$where) {
            return function($query) {};
        }
        
        return function ($query) use ($where) {
            foreach ($where as $condition) {
                $field = $condition["field"];
                $op = $condition["op"];
                $value = $condition["value"];
                
                switch ($op) {
                    case 'like':
                        $query->where($field, 'like', "%$value%");
                        break;
                    case 'like_left':
                        $query->where($field, 'like', "%$value");
                        break;
                    case 'like_right':
                        $query->where($field, 'like', "$value%");
                        break;
                    case 'datetime_range_int':
                    case 'date_range_int':
                        $whereStr = "1 = 1 ";
                        if (!empty($value[0])) {
                            $startTime = strtotime($value[0]) * 1000;
                            $whereStr .= " and $field >= $startTime ";
                        }
                        if (!empty($value[1])) {
                            $endTime = ($op == 'date_range_int') 
                                ? strtotime($value[1] . " 23:59:59") * 1000
                                : strtotime($value[1]) * 1000;
                            $whereStr .= " and $field <= $endTime";
                        }
                        $query->whereRaw($whereStr);
                        break;
                    case 'range':
                        if (!empty($value[0])) {
                            $query->where($field, '>=', $value[0]);
                        }
                        if (!empty($value[1])) {
                            $query->where($field, '<=', $value[1]);
                        }
                        break;
                    case 'in':
                        if (is_array($value) && !empty($value)) {
                            $query->whereIn($field, $value);
                        }
                        break;
                    case 'not in':
                        if (is_array($value) && !empty($value)) {
                            $query->whereNotIn($field, $value);
                        }
                        break;
                    default:
                        $query->where($field, $op, $value);
                        break;
                }
            }
        };
    }

    /**
     * 获取分页参数
     * @return array
     */
    protected function getPageParams()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 15);
        
        return [
            'page' => max(1, intval($page)),
            'limit' => max(1, min(100, intval($limit))) // 限制最大100条
        ];
    }

    /**
     * 格式化分页数据
     * @param object $paginate 分页对象
     * @return array
     */
    protected function formatPageData($paginate)
    {
        return [
            'list' => $paginate->items(),
            'total' => $paginate->total(),
            'per_page' => $paginate->listRows(),
            'current_page' => $paginate->currentPage(),
            'last_page' => $paginate->lastPage(),
            'has_more' => $paginate->hasPages()
        ];
    }

    /**
     * 验证必填参数
     * @param array $params 参数数组
     * @param array $required 必填字段
     * @return void
     */
    protected function validateRequired($params, $required)
    {
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                $this->error("参数 {$field} 不能为空");
            }
        }
    }

    /**
     * 获取当前登录用户ID
     * @return string|null
     */
    protected function getUserId()
    {
        // 从token中获取用户ID
        $token = $this->request->header('Authorization');
        if (!$token) {
            return null;
        }

        $userInfo = $this->parseToken($token);
        return $userInfo['LOGIN_USER_ID'] ?? null;
    }

    /**
     * 解析token获取用户信息
     * @param string $token
     * @return array|null
     */
    protected function parseToken($token)
    {
        try {
            // 移除 Bearer 前缀
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            // 简单的base64解码（实际项目中应该使用JWT或其他安全方式）
            $decoded = base64_decode($token);
            $userInfo = json_decode($decoded, true);

            return $userInfo;
        } catch (\Exception $e) {
            error_log("Token parse error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取当前登录用户信息
     * @return array
     */
    protected function getUser()
    {
        return $this->user ?? [];
    }

    /**
     * 记录操作日志 (旧版本，保持兼容性)
     * @param string $action 操作动作
     * @param string $content 操作内容
     * @param array $data 相关数据
     * @return void
     */
    protected function logAction($action, $content, $data = [])
    {
        try {
            Db::table($this->getModeTableName('TD_LOG'))->insert([
                'USER_ID' => $this->getUserId(),
                'ACTION' => $action,
                'CONTENT' => $content,
                'DATA' => json_encode($data),
                'IP' => $this->request->ip(),
                'CREATE_TIME' => time() * 1000,
                'USER_AGENT' => $this->request->header('User-Agent')
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 写入系统日志到 sys_log 表
     * @param string $action 操作类型
     * @param string $description 描述
     * @param array $data 附加数据
     * @return void
     */
    protected function writeSystemLog($action, $description, $data = [])
    {
        try {
            $logData = [
                'action' => $action,
                'description' => $description,
                'user_id' => $this->getUserId() ?? ($data['username'] ?? ''),
                'ip_address' => $this->request->ip(),
                'user_agent' => $this->request->header('User-Agent'),
                'request_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s'),
                'timestamp' => time() * 1000
            ];

            Db::table($this->getModeTableName('sys_log'))->insert($logData);

        } catch (\Exception $e) {
            // 日志写入失败不影响主流程，但记录到PHP错误日志
            error_log("系统日志写入失败: " . $e->getMessage() . " - 原始日志: {$action} - {$description}");
        }
    }
}
